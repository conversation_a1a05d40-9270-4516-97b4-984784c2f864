package com.ly.titc.pms.member.mediator.entity.dto.store;

import com.ly.titc.pms.member.dubbo.entity.response.MasterObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-15 13:51
 */
@Data
@Accessors(chain = true)
public class StoreUsageRuleDto {

    /**
     * 使用规则模式
     * SINGLE 唯一
     * MULTIPLE 混合
     */
    private String ruleMode;

    /**
     * 规则ID
     */
    private String usageRuleId;

    /**
     * 储值使用模式
     * 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    private Integer usageMode;

    /**
     * 储值使用模式值
     */
    private List<MasterObject> usageModeScopes;

    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    private List<String> scopePlatformChannels;

    /**
     * 规则名称
     */
    private String usageRuleName;

    /**
     * 扣减类型
     */
    private String deductionType;

    /**
     * 扣减比例
     */
    private String deductionRatio;

    /**
     * 使用是否需要密码 1：需要 0 不需要
     */
    private Integer isUsePassword;

    /**
     * 使用其他会员储值 1 支持 0 不支持
     */
    private Integer isUseOtherMember;

    /**
     * 积分是否可用
     */
    private Integer isCanUse;
}
