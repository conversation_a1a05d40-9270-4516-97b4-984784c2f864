package com.ly.titc.pms.member.mediator.handler.task.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.pms.member.biz.CardLevelConfigBiz;
import com.ly.titc.pms.member.biz.CardLevelUpgradeRuleBiz;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.ScheduleHandlerEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.handler.task.AbstractMemberTaskHandler;
import com.ly.titc.pms.member.mediator.handler.task.MemberRecordsCache;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/2 20:38
 */
@Slf4j
@Component
public class MemberUpgradeRuleTaskHandler extends AbstractMemberTaskHandler {

    @Resource
    private CardLevelUpgradeRuleBiz cardLevelUpgradeRuleBiz;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private ScheduleMedConverter scheduleMedConverter;
    @Resource
    private CardConfigMedService cardConfigMedService;
    @Resource
    private MemberCardMedService memberCardMedService;
    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Resource
    private MemberRecordsCache memberRecordsCache;

    @Override
    public Integer getTaskType() {
        return ScheduleHandlerEnum.MEMBER_UPGRADE_RULE.getAction();
    }

    @Override
    public List<String> filterBloc() {
        return ConfigCenterUtil.listBloc();
    }

    @Override
    public Pageable<String> filterMemberNo(Integer masterType, String masterCode, Integer pageIndex, Integer pageSize) {
        // 升级
        List<CardLevelUpgradeRuleInfo> upgradeRuleList = cardLevelUpgradeRuleBiz.list(masterType, masterCode);
        if (CollectionUtils.isEmpty(upgradeRuleList)) {
            log.info("无升级方案：masterCode: {}", masterCode);
            return Pageable.empty();
        }
        String today = LocalDate.now().toString();
        IPage<MemberCardInfo> page = memberCardInfoBiz.pageUpgradeCheckCards(masterType, masterCode, pageIndex, pageSize, today);
        return PageableUtil.convert(page, page.getRecords().stream().map(MemberCardInfo::getMemberNo).collect(Collectors.toList()));
    }

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }
        // 查询每个卡对应的升降级规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelUpgradeRuleDto upgradeRuleDto = new ListCardLevelUpgradeRuleDto();
        upgradeRuleDto
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StatusEnum.VALID.getStatus());
        List<CardLevelUpgradeRuleDto> rules = cardConfigMedService.listCardLevelUpgradeRule(upgradeRuleDto);
        // 按照sort排序，同一个cardId-sourceLevel组合的规则按sort顺序执行
        Map<String, List<CardLevelUpgradeRuleDto>> ruleMap = rules.stream()
                .sorted(Comparator.comparing(CardLevelUpgradeRuleDto::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.groupingBy(
                        item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()),
                        Collectors.toList()
                ));
        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto();
        levelConfigDto.setMasterType(masterType).setMasterCode(masterCode).setCardIds(cardIds).setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos
                .stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));

        // 使用Spring注入的Redis缓存对象，避免重复查询
        LocalDateTime endDate = LocalDateTime.now();
        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            List<CardLevelUpgradeRuleDto> rulesForCard = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的升级规则，跳过这张卡
            if (CollectionUtils.isEmpty(rulesForCard)) {
                log.info("没有找到对应的升级规则，跳过这张卡, memberNo: {}", memberNo);
                continue;
            }
            // 按sort顺序执行所有规则
            for (CardLevelUpgradeRuleDto rule : rulesForCard) {
                MemberUpgradeRuleDto ruleDto = scheduleMedConverter.convertUpgradeRuleDto(rule);
                if (ruleDto == null) {
                    continue;
                }
                CardConditionDto cardConditionDto = new CardConditionDto();
                cardConditionDto.setMasterType(masterType);
                cardConditionDto.setMasterCode(masterCode);
                cardConditionDto.setMemberNo(memberNo);
                cardConditionDto.setCycleType(ruleDto.getCycleType());
                cardConditionDto.setRelationType(ruleDto.getUpgradeSuccessfulPerformType());
                cardConditionDto.setDetails(ruleDto.getDetails());
                ConditionCheckResult checkResult = conditionCheck(cardConditionDto);
                log.info("checkResult: {}, memberNo: {}", checkResult, memberNo);
                if (checkResult.isPassed()) {
                    CardLevelConfigWithPrivilegeDto beforeLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
                    if (beforeLevelInfo == null) {
                        log.info("会员卡等级不存在，memberNo: {}, cardId: {}", memberNo, cardId);
                        continue;
                    }
                    CardLevelConfigInfo cardLevelConfig = cardLevelConfigBiz.getByCardLevel(cardId, ruleDto.getTargetLevel());
                    if (cardLevelConfig == null) {
                        log.info("需要升级的会员卡等级不存在，memberNo: {}, targetLevel: {}", memberNo, ruleDto.getTargetLevel());
                        continue;
                    }
                    Pair<String, String> pair = CommonUtil.getCardValidDate(cardLevelConfig.getIsLongTerm(), cardLevelConfig.getValidPeriod());
                    // 升级会员卡等级
                    UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                    updateCardLevelDto.setMemberNo(memberNo)
                            .setCardId(cardId)
                            .setMemberCardNo(memberCardInfo.getMemberCardNo())
                            .setPreLevel(cardLevel)
                            .setPreLevelName(beforeLevelInfo.getCardLevelName())
                            .setAfterLevel(ruleDto.getTargetLevel())
                            .setAfterLevelName(cardLevelConfig.getCardLevelName())
                            .setEffectBeginDate(pair.getLeft())
                            .setEffectEndDate(pair.getRight())
                            .setIsLongTerm(cardLevelConfig.getIsLongTerm())
                            .setChangeType(ChangeTypeEnum.UPGRADE_AUTO.getType())
                            .setReason("自动升级：" + checkResult.generateReason())
                            .setOperator("自动升级，规则名称：" + ruleDto.getName())
                            .setRuleId(rule.getId())
                            .setSourceType("BLOC")
                            .setSourceHotel(masterCode);
                    memberCardMedService.updateCardLevel(updateCardLevelDto);
                }
            }
        }
    }
}
