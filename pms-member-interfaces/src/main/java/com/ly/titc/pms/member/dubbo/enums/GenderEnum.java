package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别枚举
 *
 * <AUTHOR>
 * @classname GenderEnum
 * @descrition 性别枚举
 * @since 2023/7/31 14:59
 */
@Getter
@AllArgsConstructor
public enum GenderEnum {


    MALE(1, "男"),
    FEMALE(2, "女"),
    SECRECY(0, "保密"),
    ;


    /**
     * 性别1：男；2：女
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static String getNameByType(Integer type) {
        for (GenderEnum genderEnum : GenderEnum.values()) {
            if (genderEnum.getType().equals(type)) {
                return genderEnum.getDesc();
            }
        }
        return null;
    }
}
