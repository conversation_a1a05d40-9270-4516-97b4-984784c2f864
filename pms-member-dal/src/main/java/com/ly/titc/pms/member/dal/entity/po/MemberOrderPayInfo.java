package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <p>
 * 会员订单支付信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MemberOrderPayInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员订单号
     */
    @PrimaryKey(column = "member_order_no", value = 1)
    private String memberOrderNo;

    @PrimaryKey(column = "member_order_pay_no", value = 2)
    private String memberOrderPayNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 订单支付状态  1 待支付，2  支付成功 3，支付失败 4.交易关闭
     */
    private Integer orderPayState;

    /**
     * 收款方主体类型 （1:集团;2:门店;3:艺龙）
     */
    private Integer payeeMasterType;

    /**
     * 收款方主体code
     */
    private String payeeMasterCode;

    /**
     * 收款方主体名称
     */
    private String payeeMasterName;

    /**
     * 支付路由 hotel_Cashier 酒店收银台  bloc_Cashier 集团收银台
     */
    private String payRoute;

    /**
     * 支付渠道：微信，支付宝，刷卡
     */
    private String payChannel;

    /**
     * 支付产品（microPay 条码支付 slotCardPay刷卡支付）
     */
    private String payProduct;

    /**
     * 支付厂商： FUIOU 富友 WX 微信 ZFB 支付宝 TALN 通联 UNP 银联
     */
    private String payVendor;

    /**
     * 支付方式： wxPay 微信支付； aliPay 支付宝支付； unionpay 银联支付； posSlotCard 国内卡刷卡； posWildcard 境外卡刷卡； posWxPay pos微信支付； posAliPay pos支付宝支付； posUnionPay pos银联
     */
    private String payType;

    /**
     * POS机终端ID
     */
    private String termId;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 支付完成时间
     */
    private String payedTime;

    /**
     * 收银台交易号
     */
    private String onlinePayNo;

    /**
     * 渠道交易号
     */
    private String transactionId;

    /**
     * 结算项code
     */
    private String itemCode;

    /**
     * 结算项名称
     */
    private String itemName;

    /**
     * 账单明细NO
     */
    private String accountItemNo;

    /**
     * 账户号
     */
    private String accountNo;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;


}
