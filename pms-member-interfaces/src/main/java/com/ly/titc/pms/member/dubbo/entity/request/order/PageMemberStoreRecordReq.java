package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：PageMemberStoreConsumeRequest
 * @Date：2024-12-9 16:13
 * @Filename：PageMemberStoreConsumeRequest
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageMemberStoreRecordReq extends BlocBaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 消费类型：PAY:消费，FREEZE, REFUND 退款
     */
    private String consumeType;

    /**
     * 渠道
     */
    private String platformChannel;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 订单状态 1 未支付 2 支付成功  3 已退款
     */
    private Integer state;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码非法，最小为1")
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    @Max(value = 100, message = "页数非法，最大为100")
    private Integer pageSize = 20;

}
