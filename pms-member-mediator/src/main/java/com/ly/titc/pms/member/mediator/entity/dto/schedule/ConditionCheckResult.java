package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 条件检查结果
 *
 * @Author：rui
 * @Date：2024-12-XX
 */
@Data
@Accessors(chain = true)
public class ConditionCheckResult {

    /**
     * 检查是否通过
     */
    private boolean passed;

    /**
     * 满足的条件详情
     */
    private List<ConditionDetail> satisfiedConditions = new ArrayList<>();

    /**
     * 不满足的条件详情
     */
    private List<ConditionDetail> unsatisfiedConditions = new ArrayList<>();

    /**
     * 生成原因描述
     */
    public String generateReason() {
        if (passed) {
            // 升级成功的原因
            if (satisfiedConditions.size() == 1) {
                return "满足" + satisfiedConditions.get(0).getDescription();
            } else {
                StringBuilder sb = new StringBuilder("满足");
                for (int i = 0; i < satisfiedConditions.size(); i++) {
                    if (i > 0) {
                        sb.append("、");
                    }
                    sb.append(satisfiedConditions.get(i).getDescription());
                }
                return sb.toString();
            }
        } else {
            // 降级的原因
            if (unsatisfiedConditions.size() == 1) {
                return "不满足" + unsatisfiedConditions.get(0).getDescription();
            } else {
                StringBuilder sb = new StringBuilder("不满足");
                for (int i = 0; i < unsatisfiedConditions.size(); i++) {
                    if (i > 0) {
                        sb.append("、");
                    }
                    sb.append(unsatisfiedConditions.get(i).getDescription());
                }
                return sb.toString();
            }
        }
    }

    /**
     * 条件详情
     */
    @Data
    @Accessors(chain = true)
    public static class ConditionDetail {
        /**
         * 条件类型
         */
        private Integer checkConditionType;

        /**
         * 条件类型名称
         */
        private String checkConditionTypeName;

        /**
         * 计算方式
         */
        private Integer checkCalculateType;

        /**
         * 计算方式名称
         */
        private String checkCalculateTypeName;

        /**
         * 条件值
         */
        private String checkConditionValue;

        /**
         * 实际值
         */
        private String actualValue;

        /**
         * 是否满足
         */
        private boolean satisfied;

        /**
         * 条件描述
         */
        public String getDescription() {
            return String.format("%s%s%s(实际值:%s)",
                    checkConditionTypeName, checkCalculateTypeName, checkConditionValue, actualValue);
        }
    }
}
