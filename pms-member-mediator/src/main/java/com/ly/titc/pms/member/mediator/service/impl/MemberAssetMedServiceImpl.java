package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.mdm.entity.request.hotel.GetHotelByVidReq;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.HotelInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.bo.PageMemberStoreParamBo;
import com.ly.titc.pms.member.mediator.converter.MemberAssetMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.service.MemberAssetMedService;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberAssetMedServiceImpl
 * @Date：2024-12-5 15:32
 * @Filename：MemberAssetMedServiceImpl
 */
@Slf4j
@Service
public class MemberAssetMedServiceImpl implements MemberAssetMedService {

    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;

    @Resource
    private MemberOrderPayInfoBiz orderPayInfoBiz;

    @Resource
    private MemberOrderRefundInfoBiz orderRefundInfoBiz;

    @Resource
    private MemberOrderDetailInfoBiz memberOrderDetailInfoBiz;

    @Resource
    private MemberAssetMedConverter converter;

    @Resource
    private AssetDecorator assetDecorator;

    @Resource
    private MemberOrderRechargeSceneInfoBiz rechargeSceneInfoBiz;

    @Resource
    private HotelDecorator hotelDecorator;
    @Resource
    private MemberInfoBiz memberInfoBiz;

    @Override
    public Pageable<MemberRechargeOrderDto> pageMemberStoredRechargeRecord(PageOrderQueryDto dto) {
        String memberNo = dto.getMemberNo();
        String trackingId = dto.getTrackingId();
        PageMemberStoreParamBo param = converter.convert(dto);
        Pageable<MemberOrderInfo> page = memberOrderInfoBiz.pageMemberStoredRechargeRecord(param);
        List<MemberOrderInfo> dataList = page.getDatas();
        if (CollectionUtil.isEmpty(dataList)) {
            return Pageable.empty();
        }
        List<String> tradeNoList = dataList.stream().map(MemberOrderInfo::getMemberOrderNo).collect(Collectors.toList());
        List<MemberOrderRechargeSceneInfo> sceneInfos = rechargeSceneInfoBiz.listByOrderNos(tradeNoList);
        List<MemberTradeConsumeRecordResp> list = assetDecorator.listRechargeConsumeRecord(memberNo, tradeNoList, trackingId);

        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(dto.getBlocCode(), null);
        Map<String, String> hotelMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName, (v1, v2) -> v1));
        return Pageable.convert(page, converter.convert(dataList, list, sceneInfos, hotelMap));
    }

    @Override
    public MemberRechargeOrderDetailDto getMemberStoredRechargeRecord(String memberOrderNo) {
        MemberOrderInfo orderInfo = memberOrderInfoBiz.getByOrderNo(memberOrderNo);
        String memberNo = orderInfo.getMemberNo();
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        MemberOrderDetailInfo memberOrderDetailInfo = memberOrderDetailInfoBiz.getByOrderNo(memberOrderNo);
        //查询支付信息
        List<MemberOrderPayInfo> orderPayInfos = orderPayInfoBiz.listByOrderNo(memberOrderNo);
        MemberOrderRechargeSceneInfo sceneInfo = rechargeSceneInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderPayInfo payInfo = null;
        if(CollectionUtil.isNotEmpty(orderPayInfos)){
            //根据创建时间排序
            orderPayInfos.sort(Comparator.comparing(MemberOrderPayInfo::getGmtCreate).reversed());
            payInfo = orderPayInfos.get(0);
        }
        //查询退款信息
        List<MemberOrderRefundInfo> refundInfos = orderRefundInfoBiz.listByMemberOrderNo(orderInfo.getMasterType(),orderInfo.getMasterCode(),memberOrderNo);

        MemberOrderRefundInfo refundInfo = null;
        if(CollectionUtil.isNotEmpty(refundInfos)){
            refundInfos.sort(Comparator.comparing(MemberOrderRefundInfo::getGmtCreate).reversed());
            refundInfo = refundInfos.get(0);
        }

        MemberRechargeOrderDetailDto detailDto = converter.convert(orderInfo, memberOrderDetailInfo,payInfo,refundInfo,sceneInfo);
        detailDto.setMemberName(memberInfo.getRealName());
        detailDto.setPlatformChannelDesc(PlatformChannelEnum.getByPlatformChannel(orderInfo.getPlatformChannel()).getPlatformChannelDesc());
        if (detailDto.getMasterType().equals(com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum.BLOC.getType())) {
            detailDto.setMasterName("集团");
        } else {
            GetHotelByVidReq req = new GetHotelByVidReq();
            req.setHotelVid(Long.valueOf(orderInfo.getMasterCode()));
            HotelInfoResp infoResp =  hotelDecorator.getHotelByVid(req);
            detailDto.setMasterName(infoResp==null?"":infoResp.getHotelName());
        }
        return detailDto;
    }
}
