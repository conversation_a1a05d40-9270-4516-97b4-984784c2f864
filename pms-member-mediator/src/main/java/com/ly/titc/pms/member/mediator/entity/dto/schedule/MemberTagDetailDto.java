package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：rui
 * @name：MemberTagDetailDto
 * @Date：2024-11-21 14:47
 * @Filename：MemberTagDetailDto
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberTagDetailDto extends ConditionCheckResult.ConditionDetail {

    /**
     * id
     */
    private Long id;

    /**
     * 打标条件类型
     */
    private Integer conditionType;

    /**
     * 计算方式
     */
    private Integer calculateType;

    /**
     * 条件值
     */
    private String conditionValue;
}
