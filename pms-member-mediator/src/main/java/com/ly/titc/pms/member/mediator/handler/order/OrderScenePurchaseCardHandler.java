package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.ActivityOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PurchaseCardDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 17:41
 */
@Slf4j
@Component
public class OrderScenePurchaseCardHandler extends AbstractOrderSceneHandler<PurchaseCardDto> {

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;

    @Resource
    private MemberCardMedConverter memberCardMedConverter;

    @Resource
    private MemberActivityDecorator memberActivityDecorator;


    @Override
    public CreateOrderDto<PurchaseCardDto> doPreCheck(CreateOrderDto<PurchaseCardDto> dto) {
        PurchaseCardDto memberDto =dto.getMemberSceneNoteDto();
        if(!Objects.isNull(dto.getActivityOrderDto())){
            ActivityOrderDto activityOrderDto = dto.getActivityOrderDto();
                     boolean flag = memberActivityDecorator.judgeAvailableMemberActivity(dto.getBlocCode(),activityOrderDto.getActivityCode(),
                    activityOrderDto.getGearCode(),memberDto.getMemberNo(),memberDto.getCardId(),memberDto.getCardLevel(),dto.getAmount());
            if (!flag) {
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00017);
            }
        }
        IssueMemberCardDto issueMemberCardDto = memberCardMedConverter.convertDtoToDto(memberDto);
        memberCardMedService.checkIssueCard(issueMemberCardDto);
        return dto;
    }

    @Override
    public String doGetLockKey(PurchaseCardDto dto) {
        return CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + String.format("%s_%s_%s", getScene(), dto.getMemberNo(), dto.getCardId());
    }

    @Override
    public void saveSceneOrder(CreateOrderDto<PurchaseCardDto> dto) {
    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        MemberOrderDetailInfo detailInfo =  detailInfoBiz.getByOrderNo(orderInfo.getMemberOrderNo());
        PurchaseCardDto purchaseCardDto = JSONObject.parseObject(detailInfo.getMemberSceneNote(), PurchaseCardDto.class);

        IssueMemberCardDto issueMemberCardDto = memberCardMedConverter.convertDtoToDto(purchaseCardDto);
        issueMemberCardDto.setIssueUser(detailInfo.getCreateUser());
        issueMemberCardDto.setChangeType(ChangeTypeEnum.UPGRADE_PURCHASE.getType());
        memberCardMedService.issueCard(issueMemberCardDto);
        // 发放礼包
        grantGiftPack(orderInfo, detailInfo, purchaseCardDto.getMemberNo());
        // 返回对象
        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(purchaseCardDto.getMemberNo());
        resultDto.setMemberCardNo(purchaseCardDto.getMemberCardNo());
        resultDto.setMemberOrderNo(orderInfo.getMemberOrderNo());
        return resultDto;
    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {

    }

    public String getScene() {
        return MemberSceneEnum.PURCHASECARD.getScene();
    }
}
