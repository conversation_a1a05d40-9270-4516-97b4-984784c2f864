package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.member.*;

import java.util.List;

/**
 * 会员卡Med服务
 *
 * <AUTHOR>
 * @date 2024/10/29 10:00
 */
public interface MemberCardMedService {

    /**
     * 查询会员的所有会员卡
     *
     * @param memberNo
     * @return
     */
    List<MemberCardInfoDto> listByMemberNo(Integer masterType, String masterCode, String memberNo);

    /**
     * 查询会员的会员卡
     *
     * @param masterType
     * @param masterCode
     * @param memberNos
     * @return
     */
    List<MemberCardInfoDto> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos);

    /**
     * 根据会员卡号查询
     *
     * @param masterType
     * @param masterCode
     * @param cardNos
     * @return
     */
    List<MemberCardInfoDto> listByCardNos(Integer masterType, String masterCode, List<String> cardNos);

    /**
     * 查询会员卡号是否存在
     *
     * @param masterType
     * @param masterCode
     * @param memberCardNo
     * @return
     */
    MemberCardInfoDto getByMemberCardNo(Integer masterType, String masterCode, String memberCardNo);

    /**
     * 修改会员卡等级
     * <p>
     * 会员不是正常状态：当前会员已注销，请修改后重新提交
     * 校验当前会员卡会员等级是否与升级前一致，如不一致提示”当前会员等级发生变动，请确认后重新升级“
     * 校验升级后会员卡&会员等级是否启用，如未启用提示“当前会员卡已停用，请重新选择会员卡”
     * 升级活动是否正确，重新查询匹配的售卡活动，如不符则（活动不同、活动状态非进行中、不包含此会员卡等级）提示”升级活动发生变更，请重新升级“
     * 黑名单校验：当前会员已加入【会员操作】黑名单，无法操作
     *
     * @param updateCardLevelDto
     */
    void checkUpdateCardLevel(UpdateCardLevelDto updateCardLevelDto);

    /**
     * 修改会员卡等级
     *
     * @param updateCardLevelDto
     */
    void updateCardLevel(UpdateCardLevelDto updateCardLevelDto);

    /**
     * 批量修改会员卡等级
     *
     * @param batchUpdateCardLevelDto
     */
    List<UpdateCardLevelResultDto> batchUpdateCardLevel(BatchUpdateCardLevelDto batchUpdateCardLevelDto);

    /**
     * 会员卡保存
     *
     * @param memberCardInfo
     */
    String updateCardInfo(UpdateMemberCardDto memberCardInfo);

    /**
     * 生成会员卡号
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @return
     */
    String generateCardNo(Integer masterType, String masterCode, Long cardId);

    /**
     * 发放会员卡
     * 会员不是正常状态：当前会员已注销，请修改后重新提交
     * 校验会员卡&会员等级是否启用，如未启用提示“当前会员卡已停用，请重新选择会员卡”
     * 售卡活动是否正确，重新查询匹配的售卡活动，如不符则（活动不同、活动状态非进行中、不包含此会员卡等级）提示”售卡活动发生变更，请重新注册“
     * 会员卡号已存在会员使用，提示“会员卡号已被使用，请更换卡号”
     * 会员已拥有会员卡，提示“不可升级为当前会员本身拥有的会员卡”
     * 黑名单校验：当前会员已加入【会员操作】黑名单，无法操
     *
     * @param memberCardInfo
     * @return
     */
    void checkIssueCard(IssueMemberCardDto memberCardInfo);

    /**
     * 发放会员卡
     *
     * @param memberCardInfo
     * @return
     */
    String issueCard(IssueMemberCardDto memberCardInfo);

    /**
     * 刷新会员卡号缓存
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     */
    void refreshCardNoCache(Integer masterType, String masterCode, Long cardId);

    /**
     * 校验会员是否拥有会员卡
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @return
     */
    boolean checkHasCard(Integer masterType, String masterCode, Long cardId, Integer cardLevel);

    /**
     * 校验会员卡号是否被占用
     *
     * @param masterType
     * @param masterCode
     * @param memberCardNo
     * @return
     */
    boolean checkCardNoExist(Integer masterType, String masterCode, String memberCardNo);

    /**
     * 校验会员是否存在该卡
     *
     * @param masterType
     * @param masterCode
     * @param memberNo
     * @param cardId
     * @return
     */
    boolean checkMemberHasCard(Integer masterType, String masterCode, String memberNo, Long cardId);

}
