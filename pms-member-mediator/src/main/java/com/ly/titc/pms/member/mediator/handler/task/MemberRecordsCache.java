package com.ly.titc.pms.member.mediator.handler.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.entity.BaseCheck;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员记录Redis缓存
 * <AUTHOR>
 * @date 2025/7/2 20:53
 */
@Slf4j
@Component
public class MemberRecordsCache {

    @Resource
    private RedisFactory redisFactory;

    /**
     * 缓存过期时间：30分钟（1800秒）
     */
    private static final int CACHE_EXPIRE_SECONDS = 1800;

    public List<BaseCheck> get(String key) {
        try {
            String redisKey = buildRedisKey(key);
            String jsonValue = redisFactory.getString(redisKey);
            if (StringUtils.hasText(jsonValue)) {
                return JSON.parseObject(jsonValue, new TypeReference<List<BaseCheck>>() {});
            }
        } catch (Exception e) {
            log.error("从Redis获取缓存数据失败, key: {}", key, e);
        }
        return null;
    }

    public void put(String key, List<BaseCheck> records) {
        try {
            String redisKey = buildRedisKey(key);
            String jsonValue = JSON.toJSONString(records);
            redisFactory.setString(redisKey, CACHE_EXPIRE_SECONDS, jsonValue);
        } catch (Exception e) {
            log.error("向Redis存储缓存数据失败, key: {}", key, e);
        }
    }

    public boolean containsKey(String key) {
        try {
            String redisKey = buildRedisKey(key);
            String value = redisFactory.getString(redisKey);
            return StringUtils.hasText(value);
        } catch (Exception e) {
            log.error("检查Redis缓存key是否存在失败, key: {}", key, e);
            return false;
        }
    }

    /**
     * 构建Redis缓存key
     */
    private String buildRedisKey(String key) {
        return CommonConstant.MEMBER_RECORDS_CACHE_PREFIX + ":" + key;
    }

    private String generateKey(String type, LocalDateTime start, LocalDateTime end) {
        return String.format("%s_%s_%s", type,
                start != null ? start.toString() : "null",
                end != null ? end.toString() : "null");
    }

    public String getPointRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("POINT", start, end);
    }

    public String getConsumptionRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("CONSUMPTION", start, end);
    }

    public String getRechargeRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("RECHARGE", start, end);
    }

    public String getCheckoutRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("CHECKOUT", start, end);
    }

    public String getStayRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("STAY", start, end);
    }

    public String getUnstayRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("UNSTAY", start, end);
    }

    public String getAvgRoomFeeRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("AVG_ROOM_FEE", start, end);
    }

    public String getRegisterDaysRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("REGISTER_DAYS", start, end);
    }

}
