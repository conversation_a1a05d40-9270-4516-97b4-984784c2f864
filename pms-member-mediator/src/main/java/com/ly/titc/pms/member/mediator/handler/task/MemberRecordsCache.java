package com.ly.titc.pms.member.mediator.handler.task;

import com.ly.titc.pms.member.com.entity.BaseCheck;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/2 20:53
 */
@Data
public class MemberRecordsCache {

    private final Map<String, List<BaseCheck>> cache = new HashMap<>();

    public List<BaseCheck> get(String key) {
        return cache.get(key);
    }

    public void put(String key, List<BaseCheck> records) {
        cache.put(key, records);
    }

    public boolean containsKey(String key) {
        return cache.containsKey(key);
    }

    private String generateKey(String type, LocalDateTime start, LocalDateTime end) {
        return String.format("%s_%s_%s", type,
                start != null ? start.toString() : "null",
                end != null ? end.toString() : "null");
    }

    public String getPointRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("POINT", start, end);
    }

    public String getConsumptionRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("CONSUMPTION", start, end);
    }

    public String getRechargeRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("RECHARGE", start, end);
    }

    public String getCheckoutRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("CHECKOUT", start, end);
    }

    public String getStayRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("STAY", start, end);
    }

    public String getUnstayRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("UNSTAY", start, end);
    }

    public String getAvgRoomFeeRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("AVG_ROOM_FEE", start, end);
    }

    public String getRegisterDaysRecordsKey(LocalDateTime start, LocalDateTime end) {
        return generateKey("REGISTER_DAYS", start, end);
    }

}
