package com.ly.titc.pms.member.mediator.help;

import com.ly.titc.pms.member.com.entity.BaseCheck;
import com.ly.titc.pms.member.mediator.handler.task.MemberRecordsCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/2 21:19
 */
@Component
@Slf4j
public class MemberTaskCacheHelper {

    @Resource
    protected MemberTaskDataStatisticsHelper memberTaskDataStatisticsHelper;

    /**
     * 批量获取会员的所有记录数据（带缓存优化）
     *
     * @param masterType 主体类型
     * @param masterCode 主体编码
     * @param memberNo 会员编号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param cache 缓存对象
     * @return 包含所有记录类型的Map
     */
    public Map<String, List<BaseCheck>> getAllMemberRecords(Integer masterType, String masterCode,
                                                               String memberNo, LocalDateTime startDate,
                                                               LocalDateTime endDate, MemberRecordsCache cache) {

        Map<String, List<BaseCheck>> result = new HashMap<>();
        List<String> memberNoList = Collections.singletonList(memberNo);

        // 积分记录
        String pointKey = cache.getPointRecordsKey(startDate, endDate);
        if (cache.containsKey(pointKey)) {
            result.put("pointRecords", cache.get(pointKey));
        } else {
            List<BaseCheck> pointRecords = memberTaskDataStatisticsHelper.getMemberPointRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(pointKey, pointRecords);
            result.put("pointRecords", pointRecords);
        }
        // 消费记录
        String consumptionKey = cache.getConsumptionRecordsKey(startDate, endDate);
        if (cache.containsKey(consumptionKey)) {
            result.put("consumptionRecords", cache.get(consumptionKey));
        } else {
            List<BaseCheck> consumptionRecords = memberTaskDataStatisticsHelper.getMemberConsumptionRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(consumptionKey, consumptionRecords);
            result.put("consumptionRecords", consumptionRecords);
        }
        // 充值记录
        String rechargeKey = cache.getRechargeRecordsKey(startDate, endDate);
        if (cache.containsKey(rechargeKey)) {
            result.put("rechargeRecords", cache.get(rechargeKey));
        } else {
            List<BaseCheck> rechargeRecords = memberTaskDataStatisticsHelper.getMemberRechargeRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(rechargeKey, rechargeRecords);
            result.put("rechargeRecords", rechargeRecords);
        }
        // 入住次数记录
        String checkoutKey = cache.getCheckoutRecordsKey(startDate, endDate);
        if (cache.containsKey(checkoutKey)) {
            result.put("checkoutRecords", cache.get(checkoutKey));
        } else {
            List<BaseCheck> checkoutRecords = memberTaskDataStatisticsHelper.getMemberCheckoutRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(checkoutKey, checkoutRecords);
            result.put("checkoutRecords", checkoutRecords);
        }
        // 房晚数记录
        String stayKey = cache.getStayRecordsKey(startDate, endDate);
        if (cache.containsKey(stayKey)) {
            result.put("stayRecords", cache.get(stayKey));
        } else {
            List<BaseCheck> stayRecords = memberTaskDataStatisticsHelper.getMemberStayRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(stayKey, stayRecords);
            result.put("stayRecords", stayRecords);
        }
        // 未入住天数记录
        String unstayKey = cache.getUnstayRecordsKey(startDate, endDate);
        if (cache.containsKey(unstayKey)) {
            result.put("unstayRecords", cache.get(unstayKey));
        } else {
            List<BaseCheck> unstayRecords = memberTaskDataStatisticsHelper.getMemberUnstayRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(unstayKey, unstayRecords);
            result.put("unstayRecords", unstayRecords);
        }
        // 平均房费记录
        String avgRoomFeeKey = cache.getAvgRoomFeeRecordsKey(startDate, endDate);
        if (cache.containsKey(avgRoomFeeKey)) {
            result.put("avgRoomFeeRecords", cache.get(avgRoomFeeKey));
        } else {
            List<BaseCheck> avgRoomFeeRecords = memberTaskDataStatisticsHelper.getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(avgRoomFeeKey, avgRoomFeeRecords);
            result.put("avgRoomFeeRecords", avgRoomFeeRecords);
        }
        // 注册天数记录
        String registerDaysKey = cache.getRegisterDaysRecordsKey(startDate, endDate);
        if (cache.containsKey(registerDaysKey)) {
            result.put("registerDaysRecords", cache.get(registerDaysKey));
        } else {
            List<BaseCheck> registerDaysRecords = memberTaskDataStatisticsHelper.getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(registerDaysKey, registerDaysRecords);
            result.put("registerDaysRecords", registerDaysRecords);
        }
        return result;
    }

}
