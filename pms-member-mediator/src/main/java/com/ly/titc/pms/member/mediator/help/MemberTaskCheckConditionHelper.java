package com.ly.titc.pms.member.mediator.help;

import com.ly.titc.pms.member.com.entity.BaseCheck;
import com.ly.titc.pms.member.com.enums.CalculateTypeEnum;
import com.ly.titc.pms.member.com.enums.ConditionTypeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 条件校验
 *
 * <AUTHOR>
 * @date 2025/7/2 20:55
 */
@Component
@Slf4j
public class MemberTaskCheckConditionHelper {

    /**
     * 检查积分条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkPointConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> pointRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(pointRecords)) {
            int totalScoreBalanceTotal = pointRecords.stream()
                    .mapToInt(e -> e.getTotalScoreBalance())
                    .sum();
            actualValue = String.valueOf(totalScoreBalanceTotal);
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("积分条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查消费金额条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkConsumptionConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> consumptionRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(consumptionRecords)) {
            BigDecimal expenseAmount = consumptionRecords
                    .stream()
                    .map(e -> e.getExpenseAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualValue = expenseAmount.toString();
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("消费条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查充值金额条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkRechargeConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> rechargeRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(rechargeRecords)) {
            BigDecimal rechargeAmount = rechargeRecords
                    .stream()
                    .map(e -> e.getTotalCapitalAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualValue = rechargeAmount.toString();
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("充值条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查入住次数条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkCheckoutConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> checkoutRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(checkoutRecords)) {
            int checkInCountTotal = checkoutRecords.stream()
                    .mapToInt(e -> e.getCheckInCount())
                    .sum();
            actualValue = String.valueOf(checkInCountTotal);
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("入住次数条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查入住房晚条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkStayConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> stayRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            int roomNightsTotal = stayRecords.stream()
                    .mapToInt(e -> e.getRoomNights())
                    .sum();
            actualValue = String.valueOf(roomNightsTotal);
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("入住房晚条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查未入住天数条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkUnstayConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> unstayRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(unstayRecords)) {
            int unstayDaysTotal = unstayRecords.stream()
                    .mapToInt(e -> e.getUnstayDays())
                    .sum();
            actualValue = String.valueOf(unstayDaysTotal);
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("未入住天数条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查注册天数条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkRegisterConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> registerRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(registerRecords)) {
            int registerDaysTotal = registerRecords.stream()
                    .mapToInt(e -> e.getRegisterDays())
                    .sum();
            actualValue = String.valueOf(registerDaysTotal);
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("注册天数条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查平均房费条件（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkAverageRoomFeeConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String member, List<BaseCheck> avgRoomFeeRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(avgRoomFeeRecords)) {
            BigDecimal avgRoomFeeTotal = avgRoomFeeRecords
                    .stream()
                    .map(e -> e.getAvgRoomFee())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualValue = avgRoomFeeTotal.toString();
        }

        boolean satisfied = calculateItem(detail.getCheckCalculateType(), detail.getCheckConditionValue(), actualValue);
        log.info("平均房费条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setCheckConditionType(detail.getCheckConditionType())
                .setCheckConditionTypeName(ConditionTypeEnum.getNameByType(detail.getCheckConditionType()))
                .setCheckCalculateType(detail.getCheckCalculateType())
                .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                .setCheckConditionValue(detail.getCheckConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 通用条件检查方法（带详细结果）
     */
    public ConditionCheckResult.ConditionDetail checkConditionWithDetail(ConditionCheckResult.ConditionDetail detail, String memberNo,
                                                                         List<BaseCheck> pointRecords, List<BaseCheck> consumptionRecords, List<BaseCheck> rechargeRecords,
                                                                         List<BaseCheck> checkoutRecords, List<BaseCheck> stayRecords, List<BaseCheck> unstayRecords,
                                                                         List<BaseCheck> averageRecords, List<BaseCheck> registerRecords) {

        switch (ConditionTypeEnum.getByType(detail.getCheckConditionType())) {
            case POINT:
                return checkPointConditionWithDetail(detail, memberNo, pointRecords);
            case CONSUME_AMOUNT:
                return checkConsumptionConditionWithDetail(detail, memberNo, consumptionRecords);
            case RECHARGE_AMOUNT:
                return checkRechargeConditionWithDetail(detail, memberNo, rechargeRecords);
            case IN_COUNT:
                return checkCheckoutConditionWithDetail(detail, memberNo, checkoutRecords);
            case IN_NIGHT:
                return checkStayConditionWithDetail(detail, memberNo, stayRecords);
            case UNCHECKED_DAYS:
                return checkUnstayConditionWithDetail(detail, memberNo, unstayRecords);
            case AVERAGE_ROOM_FEE:
                return checkAverageRoomFeeConditionWithDetail(detail, memberNo, averageRecords);
            case REGISTER_DAYS:
                return checkRegisterConditionWithDetail(detail, memberNo, registerRecords);
            default:
                return new ConditionCheckResult.ConditionDetail()
                        .setCheckConditionType(detail.getCheckConditionType())
                        .setCheckConditionTypeName("未知条件")
                        .setCheckCalculateType(detail.getCheckCalculateType())
                        .setCheckCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCheckCalculateType()))
                        .setCheckConditionValue(detail.getCheckConditionValue())
                        .setActualValue("0")
                        .setSatisfied(false);
        }
    }

    /**
     * 检查所有条件（带详细结果）
     */
    public ConditionCheckResult checkAllConditionsWithDetail(List<? extends ConditionCheckResult.ConditionDetail> details, String memberNo,
                                                             List<BaseCheck> pointRecords, List<BaseCheck> consumptionRecords, List<BaseCheck> rechargeRecords,
                                                             List<BaseCheck> checkoutRecords, List<BaseCheck> stayRecords, List<BaseCheck> unstayRecords,
                                                             List<BaseCheck> averageRecords, List<BaseCheck> registerRecords) {

        ConditionCheckResult result = new ConditionCheckResult();
        boolean allPassed = true;

        for (ConditionCheckResult.ConditionDetail detail : details) {
            ConditionCheckResult.ConditionDetail conditionResult = checkConditionWithDetail(
                    detail, memberNo, pointRecords, consumptionRecords, rechargeRecords,
                    checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords);

            if (conditionResult.isSatisfied()) {
                result.getSatisfiedConditions().add(conditionResult);
            } else {
                result.getUnsatisfiedConditions().add(conditionResult);
                allPassed = false;
            }
        }

        result.setPassed(allPassed);
        return result;
    }

    /**
     * 检查任意条件（带详细结果）
     */
    public ConditionCheckResult checkAnyConditionWithDetail(List<? extends ConditionCheckResult.ConditionDetail> details, String memberNo,
                                                            List<BaseCheck> pointRecords, List<BaseCheck> consumptionRecords, List<BaseCheck> rechargeRecords,
                                                            List<BaseCheck> checkoutRecords, List<BaseCheck> stayRecords, List<BaseCheck> unstayRecords,
                                                            List<BaseCheck> averageRecords, List<BaseCheck> registerRecords) {

        ConditionCheckResult result = new ConditionCheckResult();
        boolean anyPassed = false;

        for (ConditionCheckResult.ConditionDetail detail : details) {
            ConditionCheckResult.ConditionDetail conditionResult = checkConditionWithDetail(
                    detail, memberNo, pointRecords, consumptionRecords, rechargeRecords,
                    checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords);

            if (conditionResult.isSatisfied()) {
                result.getSatisfiedConditions().add(conditionResult);
                anyPassed = true;
                // TODO break掉
            } else {
                result.getUnsatisfiedConditions().add(conditionResult);
            }
        }

        result.setPassed(anyPassed);
        return result;
    }

    public Boolean calculateItem(Integer calculateType, String conditionValue, String factValue) {
        try {
            // 将字符串转换为 BigDecimal 进行比较
            BigDecimal conditionVal = new BigDecimal(conditionValue);
            BigDecimal factVal = new BigDecimal(factValue);
            switch (CalculateTypeEnum.getByType(calculateType)) {
                case GT_EQ:
                    return factVal.compareTo(conditionVal) >= 0;
                case GT:
                    return factVal.compareTo(conditionVal) > 0;
                case LT_EQ:
                    return factVal.compareTo(conditionVal) <= 0;
                case LT:
                    return factVal.compareTo(conditionVal) < 0;
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("计算失败", e);
            return false;
        }
    }
}
