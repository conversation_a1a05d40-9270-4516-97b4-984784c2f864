package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.enums.AmountTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 13:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateOrderBaseReq extends BaseMasterReq {

    /**
     * 集团编码
     */
    private String blocCode;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 酒店code
     */
    private String hotelCode;


    /**
     * 售价类型 POINT 积分 MONEY 钱
     */
    @NotEmpty(message = "售价类型不能为空")
    @LegalEnum(target = AmountTypeEnum.class, methodName = "getType", message = "售价类型不合法")
    private String amountType;

    /**
     * 单价 （保留到分） 支付单价
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal price;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer num;

    /**
     * 金额（保留到分） 支付金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 活动code
     */
    @NotBlank(message = "活动code不能为空")
    private String activityCode;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 档位
     */
    private String gearCode;

    /**
     * 平台渠道
     */
    @NotBlank(message = "平台渠道不能为空")
    @LegalEnum(target = PlatformChannelEnum.class, methodName = "getPlatformChannel", message = "平台渠道不合法")
    private String platformChannel;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
