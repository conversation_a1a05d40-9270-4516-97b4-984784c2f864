package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：GenderEnum
 * @Date：2024-11-19 22:58
 * @Filename：GenderEnum
 */
public enum GenderEnum {
    // 性别1：男；2：女 3:保密

    MALE(1, "男"),
    FEMALE(2, "女"),
    SECRET(0, "保密");
    private Integer type;

    private String name;

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }

    public static String getNameByType(Integer type) {
        for (GenderEnum genderEnum : GenderEnum.values()) {
            if (genderEnum.getType().equals(type)) {
                return genderEnum.getName();
            }
        }
        return null;
    }

    public static Integer getTypeByName(String name) {
        for (GenderEnum genderEnum : GenderEnum.values()) {
            if (genderEnum.getName().equals(name)) {
                return genderEnum.getType();
            }
        }
        return null;
    }

    GenderEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
