package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.cashier.dubbo.entity.request.SelectReq;
import com.ly.titc.cashier.dubbo.entity.response.SelectResp;
import com.ly.titc.cashier.dubbo.enums.CashierSceneEnum;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.*;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.dubbo.entity.request.SelectBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.PageMemberStoreRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.RechargeRecordDetailReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeRecordResp;
import com.ly.titc.pms.member.dubbo.entity.response.SelectValueResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberStoreAssetDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberStoreConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberStoreOpDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.PayCashierConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberAssetMedService;
import com.ly.titc.pms.member.mediator.service.MemberStoreAssetMedService;
import com.ly.titc.pms.member.mediator.service.StoreConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 储值资产
 *
 * <AUTHOR>
 * @date 2025/2/10 20:16
 */
@Slf4j
@Validated
@DubboService
public class MemberStoreAssetDubboServiceImpl implements MemberStoreAssetDubboService {

    @Resource(type = AssetDecorator.class)
    private AssetDecorator assetDecorator;

    @Resource
    private MemberStoreOpDecorator memberStoreOpDecorator;

    @Resource
    private MemberStoreConverter memberStoreConverter;

    @Resource
    private MemberAssetMedService memberAssetMedService;

    @Resource
    private PayCashierConfigDecorator payCashierConfigDecorator;
    @Resource
    private MemberStoreAssetMedService assetMedService;

    @Override
    public Response<List<SelectValueResp>> selectStoreScene(SelectBaseReq request) {
        SelectReq req = memberStoreConverter.convertDtoToResp(request);
        List<SelectResp> resps = payCashierConfigDecorator.selectCashierScene(req);
        List<SelectValueResp> result = new ArrayList<>();
        resps.forEach(resp -> {
            if (resp.getValue().equals(CashierSceneEnum.MEMBER_RECHARGE.getCode())) {
                return;
            }
            SelectValueResp selectValueResp = new SelectValueResp();
            selectValueResp.setText(resp.getText());
            selectValueResp.setValue(resp.getValue());
            result.add(selectValueResp);
        });
        return Response.success(result);
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> recharge(MemberStoreRechargeReq req) {
        return Response.success(memberStoreOpDecorator.recharge(req));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> rechargeRollback(MemberStoreRechargeRollBackReq req) {
        return Response.success(memberStoreOpDecorator.rechargeRollback(req));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> consumeStore(MemberStoreConsumeReq req) {

        return Response.success(assetMedService.consumeStore(req));
    }

    @Override
    public Response<MemberStoreConsumeRollBackResp> consumeRollback(MemberStoreConsumeRollBackReq req) {
        return Response.success(memberStoreOpDecorator.consumeRollback(req));
    }

    @Override
    public Response<MemberStoreConsumeCalResp> consumeStoreCal(MemberStoreConsumePreCalReq req) {
        return Response.success(memberStoreOpDecorator.consumeStoreCal(req));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> freeze(MemberStoreFreezeReq req) {
        return Response.success(memberStoreOpDecorator.freeze(req));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> unfreeze(UnfreezeConsumeRecordNoReq req) {
        return Response.success(memberStoreOpDecorator.unfreeze(req));
    }

    @Override
    public Response<MemberTotalAmountResp> getTotalAccountAmount(BaseMemberReq req) {
        return Response.success(assetDecorator.getTotalAccountAmount(req));
    }

    @Override
    public Response<MemberStoreAccountResp> getUsableMasterAccount(GetMemberUsableReq req) {
        return Response.success(assetDecorator.getStoreUsableMasterAccount(req));
    }

    @Override
    public Response<Pageable<MemberStoreConsumeRecordResp>> pageConsumeRecord(PageMemberStoreConsumeMemberReq req) {
        return Response.success(assetDecorator.pageConsumeRecord(req));
    }

    @Override
    public Response<List<MemberTradeConsumeRecordResp>> listRechargeConsumeRecord(ListRechargeConsumeRecordMemberReq req) {
        return Response.success(assetDecorator.listRechargeConsumeRecord(req));
    }

    @Override
    public Response<Pageable<MemberRechargeRecordResp>> pageRechargeRecord(PageMemberStoreRecordReq request) {
        PageOrderQueryDto dto = memberStoreConverter.convertRequestToDto(request);
        Pageable<MemberRechargeOrderDto> pageable = memberAssetMedService.pageMemberStoredRechargeRecord(dto);
        return Response.success(PageableUtil.convert(pageable, pageable.getDatas().stream()
                .map(memberStoreConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<MemberRechargeDetailResp> getRechargeDetail(RechargeRecordDetailReq request) {
        MemberRechargeOrderDetailDto dto = memberAssetMedService.getMemberStoredRechargeRecord(request.getMemberOderNo());
        return Response.success(memberStoreConverter.convertDtoToResp(dto));
    }
}
