package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberScheduleMqDto
 * @Date：2024-12-15 0:48
 * @Filename：MemberScheduleMqDto
 */
@Data
@Accessors(chain = true)
public class MemberScheduleMqDto {

    private Integer masterType;

    private String masterCode;

    private List<String> memberNo;

    private Integer action;
}
