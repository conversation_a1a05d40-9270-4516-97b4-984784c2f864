package com.ly.titc.pms.member.mediator.strategy;

import com.alibaba.fastjson.JSON;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.mediator.converter.RegisterMemberConverter;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.common.exceptions.ServiceException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 客户转会员策略
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
@Slf4j
@Component
public class CustomerToMemberStrategy extends AbstractMemberCardIssueStrategy {

    @Autowired
    private CustomerDecorator customerDecorator;

    @Autowired
    private MemberInfoBiz memberInfoBiz;

    @Autowired
    private RegisterMemberConverter registerMemberConverter;

    @Autowired
    private MemberMedService memberMedService;



    @Override
    public boolean supports(IssueMemberCardRequestDto dto) {
        return StringUtils.isNotBlank(dto.getCustomerNo()) && StringUtils.isBlank(dto.getMemberNo());
    }

    @Override
    protected void validateRequest(IssueMemberCardRequestDto dto) {
        // 调用父类通用校验
        super.validateRequest(dto);
        // 客户转会员特有校验
        if (StringUtils.isBlank(dto.getCustomerNo())) {
            throw new ServiceException(RespCodeEnum.CODE_400);
        }
    }

    @Override
    protected IssueMemberCardResultDto doExecute(IssueMemberCardRequestDto dto) {
        String customerNo = dto.getCustomerNo();
        // 查询客户信息
        CustomerDetailInfoResp customerInfo = getCustomerInfo(dto);
        if (customerInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10046);
        }
        // 检查客户信息中是否已经有会员号
        if (StringUtils.isNotBlank(customerInfo.getMemberNo())) {
            // 客户已经是会员，走升级逻辑
            log.info("客户{}已经是会员，会员号：{}，走升级逻辑", customerNo, customerInfo.getMemberNo());
            return handleExistingMember(dto, customerInfo);
        }
        // 客户转会员注册逻辑
        log.info("客户{}不是会员，走注册逻辑", customerNo);
        return registerCustomerAsMember(dto, customerInfo);
    }

    /**
     * 获取客户信息
     */
    private CustomerDetailInfoResp getCustomerInfo(IssueMemberCardRequestDto dto) {
        GetByCustomerNoReq customerReq = new GetByCustomerNoReq();
        customerReq.setBlocCode(dto.getMasterCode());
        customerReq.setCustomerNo(dto.getCustomerNo());
        return customerDecorator.getDetailByCustomerNo(customerReq);
    }

    /**
     * 处理已存在会员的情况
     */
    private IssueMemberCardResultDto handleExistingMember(IssueMemberCardRequestDto dto, CustomerDetailInfoResp customerInfo) {
        // 获取会员信息
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(dto.getMasterType(), dto.getMasterCode(), customerInfo.getMemberNo());
        if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.info("会员不存在或者已经被注销，则还是走客户转会员注册的逻辑, customer: {}, memberNo: {}", 
                    dto.getCustomerNo(), customerInfo.getMemberNo());
            return registerCustomerAsMember(dto, customerInfo);
        }
        // 走会员升级逻辑
        return handleExistingMemberUpgrade(dto, memberInfo);
    }

    /**
     * 处理已存在会员的升级逻辑
     */
    private IssueMemberCardResultDto handleExistingMemberUpgrade(IssueMemberCardRequestDto dto, MemberInfo existingMember) {
        String memberNo = existingMember.getMemberNo();
        // 调用抽象基类的通用方法
        return handleExistingMemberCardOperation(dto, memberNo);
    }

    /**
     * 客户转会员注册
     */
    private IssueMemberCardResultDto registerCustomerAsMember(IssueMemberCardRequestDto dto, CustomerDetailInfoResp customerInfo) {
        // 校验，必须要有手机号信息
        if (StringUtils.isBlank(customerInfo.getMobile())) {
            throw new ServiceException(RespCodeEnum.MOBILE_NOT_EXIST);
        }
        // 构建注册会员DTO
        RegisterMemberDto registerDto = buildRegisterMemberDto(dto, customerInfo);
        // 执行会员注册
        log.info("客人转会员，registerDto: {}", JSON.toJSONString(registerDto));
        RegisterMemberResultDto registerResult = memberMedService.register(registerDto);
        // 获取等级名称
        String cardLevelName = registerDto.getMemberCardInfo().getCardLevelName();
        return IssueMemberCardResultDto.success(
                registerResult.getMemberNo(),
                registerResult.getMemberCardNo(),
                dto.getCardLevel(),
                cardLevelName,
                "REGISTER"
        );
    }

    /**
     * 构建注册会员DTO
     */
    private RegisterMemberDto buildRegisterMemberDto(IssueMemberCardRequestDto dto, CustomerDetailInfoResp customerInfo) {
        // 使用RegisterMemberConverter转换基本属性
        RegisterMemberDto registerDto = registerMemberConverter.buildRegisterMemberDto(dto, customerInfo);
        // 构建会员卡信息
        IssueMemberCardDto memberCardInfo = buildMemberCardInfo(dto);
        registerDto.setMemberCardInfo(memberCardInfo);
        // 构建客户信息
        CustomerInfoDto customerInfoDto = registerMemberConverter.buildCustomerInfoDto(dto);
        registerDto.setCustomerInfo(customerInfoDto);
        return registerDto;
    }

}
