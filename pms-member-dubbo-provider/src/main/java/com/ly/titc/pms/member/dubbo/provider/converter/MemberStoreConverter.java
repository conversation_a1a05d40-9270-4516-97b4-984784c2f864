package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.cashier.dubbo.entity.request.SelectReq;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.dubbo.entity.request.SelectBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.PageMemberStoreRecordReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeRecordResp;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:22
 */
@Mapper(componentModel = "spring")
public interface MemberStoreConverter extends BaseConverter {
    PageOrderQueryDto convertRequestToDto(PageMemberStoreRecordReq request);

    @Mappings({
            @Mapping(target = "gmtCreate", source = "gmtCreate", qualifiedByName = "formatTimestamp")
    })
    MemberRechargeRecordResp convertDtoToResp(MemberRechargeOrderDto memberRechargeOrderDto);

    MemberRechargeDetailResp convertDtoToResp(MemberRechargeOrderDetailDto dto);

    SelectReq convertDtoToResp(SelectBaseReq request);
}
