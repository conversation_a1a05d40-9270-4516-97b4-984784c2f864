package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dubbo.entity.request.card.UpdateCardInfoReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.UpdateCardLevelReq;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberCardInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateMemberCardDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;

/**
 * 会员卡转换
 * 
 * <AUTHOR>
 * @date 2024/10/31 11:47
 */
@Mapper(componentModel = "spring")
public interface MemberCardConverter {

    UpdateCardLevelDto convertReqToDto(UpdateCardLevelReq request);

    @Mappings({
            @Mapping(target = "operator", source = "operator")
    })
    UpdateMemberCardDto convertReqToPo(UpdateCardInfoReq request);
}
