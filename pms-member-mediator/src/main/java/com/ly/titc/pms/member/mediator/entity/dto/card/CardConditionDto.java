package com.ly.titc.pms.member.mediator.entity.dto.card;

import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDetailDto;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: luyan
 * @create: 2025-07-04 15:07
 **/
@Data
public class CardConditionDto {

    private Integer masterType;

    private String masterCode;

    private String memberNo;

    private Integer cycleType;

    /**
     * 保级成功执行类型：ALL-全部条件;ANY-满足任一个条件
     */
    private String relationType;

    private List<? extends ConditionCheckResult.ConditionDetail> details;
}
