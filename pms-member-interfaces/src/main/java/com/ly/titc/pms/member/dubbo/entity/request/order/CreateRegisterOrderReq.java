package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 13:49
 */
@Data
@Accessors(chain = true)
public class CreateRegisterOrderReq extends CreateOrderBaseReq {

    /**
     * 会员注册信息
     */
    @Valid
    private MemberRegisterReq registerRequest;

}
