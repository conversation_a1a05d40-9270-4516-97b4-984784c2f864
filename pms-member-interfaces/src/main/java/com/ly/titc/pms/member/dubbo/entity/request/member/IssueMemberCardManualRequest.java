package com.ly.titc.pms.member.dubbo.entity.request.member;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 手动发放会员卡请求
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
public class IssueMemberCardManualRequest {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 集团编码
     */
    private String blocCode;

    /**
     * 客户编号（客人转会员时传入）
     */
    private String customerNo;

    /**
     * 会员号（已有会员升级时传入）
     */
    private String memberNo;

    /**
     * 是否发放默认卡
     * 0-否 1-是
     */
    private Integer defaultCard = 0;

    /**
     * 会员卡ID（默认卡type为0，则需要传入发放的会员卡id）
     */
    private Long cardId;

    /**
     * 需要发放的会员卡等级
     */
    private Integer cardLevel;

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    /**
     * 业务id(必传)
     */
    @NotBlank(message = "业务ID不能为空")
    private String bizNo;

    /**
     * 发放原因
     */
    private String reason;


    /**
     * 操作人
     */
    private String operator;
}
