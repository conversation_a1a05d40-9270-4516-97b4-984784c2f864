package com.ly.titc.pms.member.mediator.handler.check;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：rui
 * @name：VerifyCodeCheckHandler
 * @Date：2024-11-25 16:33
 * @Filename：VerifyCodeCheckHandler
 */
@Component
@Slf4j
public class IdNoCheckHandler extends AbstractRegisterCheckHandler {

    @Resource
    private MemberInfoBiz memberInfoBiz;

    @Override
    public Integer getAction() {
        return MemberRegisterCheckEnum.ID_NO.getAction();
    }

    @Override
    public void check(RegisterMemberDto dto) {

        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode(), traceNo = TraceNoUtil.getTraceNo(), mobile = dto.getMobile();

        MemberInfo memberInfo = memberInfoBiz.getByIdNo(masterType, masterCode, dto.getIdType(), dto.getIdNo());
        if (memberInfo != null) {
            throw new ServiceException(String.format("证件号【%s】已注册！", dto.getIdNo()), RespCodeEnum.CODE_400.getCode());
        }

    }

}
