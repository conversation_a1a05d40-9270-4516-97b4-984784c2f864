package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreConsumeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreRecordOPResultResp;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.PasswordEncryptUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.mediator.entity.dto.store.StoreConfigDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberStoreOpDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MemberStoreAssetMedService;
import com.ly.titc.pms.member.mediator.service.StoreConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/4
 */
@Slf4j
@Service
public class MemberStoreAssetMedServiceImpl implements MemberStoreAssetMedService {

    @Resource
    private StoreConfigMedService  configMedService;


    @Resource
    private MemberStoreOpDecorator memberStoreOpDecorator;

    @Resource
    private MemberInfoBiz memberInfoBiz;

    @Override
    public MemberStoreRecordOPResultResp consumeStore(MemberStoreConsumeReq req) {
        if(StringUtils.isNotEmpty(req.getPassword())){
            MemberInfo memberInfo =  memberInfoBiz.getByMemberNo(req.getMemberNo());
            if(!memberInfo.getPassword().equals(PasswordEncryptUtil.encrypt(req.getPassword()))){
                throw new ServiceException("密码不正确",RespCodeEnum.CODE_400.getCode());
            }
        }

       return memberStoreOpDecorator.consumeStore(req);
    }
}
