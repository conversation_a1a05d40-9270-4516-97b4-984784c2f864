package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 10:20
 */
@Getter
@AllArgsConstructor
public enum MemberStoreDeductionTypeEnum {
    BALANCE_FIRST(1, "优先本金"),
    GIFT_FIRST(2, "优先礼金"),
    RATE(3, "比例扣减"),

    ;

    private Integer value;

    private String desc;

    public static MemberStoreDeductionTypeEnum getByValue(Integer value) {
        for (MemberStoreDeductionTypeEnum memberStoreUsageModeEnum : MemberStoreDeductionTypeEnum.values()) {
            if (memberStoreUsageModeEnum.getValue().equals(value)) {
                return memberStoreUsageModeEnum;
            }
        }
        return null;
    }
}
