package com.ly.titc.pms.member.mediator.help;

import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStorePeriodResp;
import com.ly.titc.pms.member.biz.MemberCheckInRecordBiz;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.com.entity.BaseCheck;
import com.ly.titc.pms.member.com.function.BatchProcessor;
import com.ly.titc.pms.member.com.utils.PageUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.entity.bo.PageMemberCheckInParamBo;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员数据统计
 * 
 * <AUTHOR>
 * @date 2025/7/2 20:58
 */
@Component
@Slf4j
public class MemberTaskDataStatisticsHelper {

    @Resource
    private AssetDecorator assetDecorator;

    @Resource
    private MemberCheckInRecordBiz checkInRecordBiz;
    
    @Resource
    private MemberInfoBiz memberInfoBiz;
    
    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员积分记录
     */
    public BaseCheck getMemberPointRecordByMemberNo(String memberNo, LocalDateTime start, LocalDateTime end) {
        BaseCheck check = new BaseCheck();
        // 将LocalDateTime转换为String格式，因为AssetDecorator的方法需要String参数
        String startDate = start.toLocalDate().toString();
        String endDate = end.toLocalDate().toString();
        // 调用资产服务获取会员积分统计数据
        MemberPointPeriodResp pointPeriodResp = assetDecorator.getTotalAccountPointsPeriodList(memberNo, startDate, endDate);
        // 如果返回结果为空或没有数据，返回空列表
        if (pointPeriodResp == null || pointPeriodResp.getTotalScore() == null) {
            return check;
        }
        check.setTotalScoreBalance(pointPeriodResp.getTotalScore());
        return check;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员消费金额记录
     */
    public List<BaseCheck> getMemberConsumptionRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        List<BaseCheck> resultList = new ArrayList<>();
        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }
        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString();
        String endDate = end.toLocalDate().toString();
        // 为每个会员查询入住记录并计算消费金额
        for (String memberNo : memberNoList) {
            try {
                // 使用分页查询方法获取该会员在指定时间范围内的入住记录
                PageMemberCheckInParamBo paramBo = new PageMemberCheckInParamBo();
                paramBo.setMemberNo(memberNo)
                        .setCheckInBeginTime(startDate)
                        .setCheckInEndTime(endDate);
                // 查询所有符合条件的入住记录
                List<MemberCheckInRecord> checkInRecords = PageUtil.queryAll((int pageIndex, int pageSize) ->
                        checkInRecordBiz.pageCheckInRecord(paramBo.setPageIndex(pageIndex).setPageSize(pageSize)), null);
                BaseCheck dto = new BaseCheck();
                dto.setMemberNo(memberNo);
                // 计算入住消费金额：房费 + 其他费用
                BigDecimal checkInExpense = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(checkInRecords)) {
                    checkInExpense = checkInRecords.stream()
                            .map(record -> {
                                BigDecimal roomRate = record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO;
                                BigDecimal otherRate = record.getOtherRate() != null ? record.getOtherRate() : BigDecimal.ZERO;
                                return roomRate.add(otherRate);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                dto.setExpenseAmount(checkInExpense);
                resultList.add(dto);
            } catch (Exception e) {
                // 记录异常但继续处理其他会员
                log.warn("查询会员消费记录失败, memberNo: {}, error: {}", memberNo, e.getMessage());
            }
        }
        return resultList;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员充值金额
     */
    public List<BaseCheck> getMemberRechargeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";
        MemberStorePeriodResp resp = assetDecorator.getMemeberStorePeriodList(memberNoList, startDate, endDate);
        if (resp == null || resp.getMemberStorePeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheck列表
        return resp.getMemberStorePeriodList().stream().map(item -> {
            BaseCheck dto = new BaseCheck();
            dto.setMemberNo(item.getMemberNo());
            dto.setTotalCapitalAmount(item.getTotalAmountResp() != null ? item.getTotalAmountResp().getTotalCapitalAmount() : BigDecimal.ZERO);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住次数
     */
    public List<BaseCheck> getMemberCheckoutRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);
            // 按会员编号分组统计入住次数
            Map<String, Long> memberCheckInCountMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo, Collectors.counting()));
            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheck dto = new BaseCheck();
                dto.setMemberNo(memberNo);
                dto.setCheckInCount(memberCheckInCountMap.getOrDefault(memberNo, 0L).intValue());
                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住房晚
     */
    public List<BaseCheck> getMemberStayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);

            // 按会员编号分组统计房晚数
            Map<String, Integer> memberRoomNightsMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo,
                            Collectors.summingInt(MemberCheckInRecord::getRoomNights)));

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheck dto = new BaseCheck();
                dto.setMemberNo(memberNo);
                dto.setRoomNights(memberRoomNightsMap.getOrDefault(memberNo, 0));
                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员未入住天数
     */
    public List<BaseCheck> getMemberUnstayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询会员最近一次入住记录
            List<MemberCheckInRecord> lastRecords = checkInRecordBiz.listLastByMemberNos(memberNos);
            // 按会员编号建立映射
            Map<String, MemberCheckInRecord> memberLastRecordMap = lastRecords.stream()
                    .collect(Collectors.toMap(MemberCheckInRecord::getMemberNo, record -> record));

            LocalDate currentDate = LocalDate.now();
            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheck dto = new BaseCheck();
                dto.setMemberNo(memberNo);
                MemberCheckInRecord lastRecord = memberLastRecordMap.get(memberNo);
                if (lastRecord != null && lastRecord.getCheckOutDate() != null) {
                    try {
                        // 解析最后一次离店日期
                        LocalDate lastCheckOutDate = LocalDate.parse(lastRecord.getCheckOutDate());
                        // 计算未入住天数（当前日期 - 最后离店日期）
                        long unstayDays = ChronoUnit.DAYS.between(lastCheckOutDate, currentDate);
                        // 确保未入住天数不为负数
                        dto.setUnstayDays((int) Math.max(0, unstayDays));
                    } catch (Exception e) {
                        log.warn("解析离店日期失败, memberNo: {}, checkOutDate: {}", memberNo, lastRecord.getCheckOutDate());
                        dto.setUnstayDays(0);
                    }
                } else {
                    // 如果没有入住记录，设置为一个较大的值
                    dto.setUnstayDays(Integer.MAX_VALUE);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取平均房费
     */
    public List<BaseCheck> getMemberAvgRoomFeeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);

            // 按会员编号分组计算平均房费
            Map<String, List<MemberCheckInRecord>> memberRecordsMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo));

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheck dto = new BaseCheck();
                dto.setMemberNo(memberNo);

                List<MemberCheckInRecord> memberRecords = memberRecordsMap.get(memberNo);
                if (memberRecords != null && !memberRecords.isEmpty()) {
                    // 计算总房费和总房晚数
                    BigDecimal totalRoomFee = memberRecords.stream()
                            .map(record -> record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    int totalRoomNights = memberRecords.stream()
                            .mapToInt(MemberCheckInRecord::getRoomNights)
                            .sum();

                    // 计算平均房费：总房费 / 总房晚数
                    if (totalRoomNights > 0) {
                        BigDecimal avgRoomFee = totalRoomFee.divide(BigDecimal.valueOf(totalRoomNights), 2, BigDecimal.ROUND_HALF_UP);
                        dto.setAvgRoomFee(avgRoomFee);
                    } else {
                        dto.setAvgRoomFee(BigDecimal.ZERO);
                    }
                } else {
                    // 如果没有入住记录，平均房费为0
                    dto.setAvgRoomFee(BigDecimal.ZERO);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取注册天数
     */
    public List<BaseCheck> getMemberRegisterDaysRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询会员信息
            List<MemberInfo> memberInfos = memberInfoBiz.listByMemberNos(masterType, masterCode, memberNos);

            // 按会员编号建立映射
            Map<String, MemberInfo> memberInfoMap = memberInfos.stream()
                    .collect(Collectors.toMap(MemberInfo::getMemberNo, info -> info));

            LocalDate currentDate = LocalDate.now();

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheck dto = new BaseCheck();
                dto.setMemberNo(memberNo);

                MemberInfo memberInfo = memberInfoMap.get(memberNo);
                if (memberInfo != null && memberInfo.getGmtCreate() != null) {
                    // 计算注册天数（当前日期 - 注册日期）
                    LocalDate registerDate = memberInfo.getGmtCreate().toLocalDate();
                    long registerDays = ChronoUnit.DAYS.between(registerDate, currentDate);
                    dto.setRegisterDays((int) Math.max(0, registerDays));
                } else {
                    dto.setRegisterDays(0);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 批量处理会员记录的工具方法
     *
     * @param memberNoList 会员编号列表
     * @param start        开始时间
     * @param end          结束时间
     * @param processor    处理函数，接收会员编号列表和时间范围，返回BaseCheck列表
     * @param batchSize    批处理大小，默认50
     * @return 处理结果列表
     */
    private List<BaseCheck> processMemberRecordsBatch(List<String> memberNoList, LocalDateTime start, LocalDateTime end,
                                                         BatchProcessor processor, int batchSize) {
        List<BaseCheck> resultList = new ArrayList<>();

        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }

        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";

        // 分批处理会员列表
        for (int i = 0; i < memberNoList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, memberNoList.size());
            List<String> batchMemberNos = memberNoList.subList(i, endIndex);
            try {
                List<BaseCheck> batchResults = processor.process(batchMemberNos, startDate, endDate);
                resultList.addAll(batchResults);
            } catch (Exception e) {
                log.warn("批量处理会员记录失败, memberNos: {}, error: {}", batchMemberNos, e.getMessage());
                // 为失败的批次创建默认记录
                for (String memberNo : batchMemberNos) {
                    BaseCheck dto = new BaseCheck();
                    dto.setMemberNo(memberNo);
                    resultList.add(dto);
                }
            }
        }
        return resultList;
    }

    /**
     * 批量处理会员记录的工具方法（使用默认批处理大小50）
     */
    private List<BaseCheck> processMemberRecordsBatch(List<String> memberNoList, LocalDateTime start, LocalDateTime end,
                                                         BatchProcessor processor) {
        return processMemberRecordsBatch(memberNoList, start, end, processor, 50);
    }

}
