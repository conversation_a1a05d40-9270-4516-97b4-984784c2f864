package com.ly.titc.pms.member.mediator.help;

import com.ly.titc.pms.member.com.entity.BaseCheck;
import com.ly.titc.pms.member.mediator.handler.task.MemberRecordsCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/2 21:19
 */
@Component
@Slf4j
public class MemberTaskCacheHelper {

    @Resource
    protected MemberTaskDataStatisticsHelper memberTaskDataStatisticsHelper;

    @Resource
    private MemberRecordsCache memberRecordsCache;

    /**
     * 批量获取会员的所有记录数据（带Redis缓存优化）
     *
     * @param masterType 主体类型
     * @param masterCode 主体编码
     * @param memberNo 会员编号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 包含所有记录类型的Map
     */
    public Map<String, List<BaseCheck>> getAllMemberRecords(Integer masterType, String masterCode,
                                                               String memberNo, LocalDateTime startDate,
                                                               LocalDateTime endDate) {

        Map<String, List<BaseCheck>> result = new HashMap<>();
        // 积分记录
        String pointKey = memberRecordsCache.getPointRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(pointKey)) {
            result.put("pointRecords", memberRecordsCache.get(pointKey));
        } else {
            BaseCheck pointRecords = memberTaskDataStatisticsHelper.getMemberPointRecordByMemberNo(memberNo, startDate, endDate);
            memberRecordsCache.put(pointKey, pointRecords);
            result.put("pointRecords", pointRecords);
        }
        // 消费记录
        String consumptionKey = memberRecordsCache.getConsumptionRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(consumptionKey)) {
            result.put("consumptionRecords", memberRecordsCache.get(consumptionKey));
        } else {
            List<BaseCheck> consumptionRecords = memberTaskDataStatisticsHelper.getMemberConsumptionRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(consumptionKey, consumptionRecords);
            result.put("consumptionRecords", consumptionRecords);
        }
        // 充值记录
        String rechargeKey = memberRecordsCache.getRechargeRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(rechargeKey)) {
            result.put("rechargeRecords", memberRecordsCache.get(rechargeKey));
        } else {
            List<BaseCheck> rechargeRecords = memberTaskDataStatisticsHelper.getMemberRechargeRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(rechargeKey, rechargeRecords);
            result.put("rechargeRecords", rechargeRecords);
        }
        // 入住次数记录
        String checkoutKey = memberRecordsCache.getCheckoutRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(checkoutKey)) {
            result.put("checkoutRecords", memberRecordsCache.get(checkoutKey));
        } else {
            List<BaseCheck> checkoutRecords = memberTaskDataStatisticsHelper.getMemberCheckoutRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(checkoutKey, checkoutRecords);
            result.put("checkoutRecords", checkoutRecords);
        }
        // 房晚数记录
        String stayKey = memberRecordsCache.getStayRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(stayKey)) {
            result.put("stayRecords", memberRecordsCache.get(stayKey));
        } else {
            List<BaseCheck> stayRecords = memberTaskDataStatisticsHelper.getMemberStayRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(stayKey, stayRecords);
            result.put("stayRecords", stayRecords);
        }
        // 未入住天数记录
        String unstayKey = memberRecordsCache.getUnstayRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(unstayKey)) {
            result.put("unstayRecords", memberRecordsCache.get(unstayKey));
        } else {
            List<BaseCheck> unstayRecords = memberTaskDataStatisticsHelper.getMemberUnstayRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(unstayKey, unstayRecords);
            result.put("unstayRecords", unstayRecords);
        }
        // 平均房费记录
        String avgRoomFeeKey = memberRecordsCache.getAvgRoomFeeRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(avgRoomFeeKey)) {
            result.put("avgRoomFeeRecords", memberRecordsCache.get(avgRoomFeeKey));
        } else {
            List<BaseCheck> avgRoomFeeRecords = memberTaskDataStatisticsHelper.getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(avgRoomFeeKey, avgRoomFeeRecords);
            result.put("avgRoomFeeRecords", avgRoomFeeRecords);
        }
        // 注册天数记录
        String registerDaysKey = memberRecordsCache.getRegisterDaysRecordsKey(startDate, endDate);
        if (memberRecordsCache.containsKey(registerDaysKey)) {
            result.put("registerDaysRecords", memberRecordsCache.get(registerDaysKey));
        } else {
            List<BaseCheck> registerDaysRecords = memberTaskDataStatisticsHelper.getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            memberRecordsCache.put(registerDaysKey, registerDaysRecords);
            result.put("registerDaysRecords", registerDaysRecords);
        }
        return result;
    }

}
