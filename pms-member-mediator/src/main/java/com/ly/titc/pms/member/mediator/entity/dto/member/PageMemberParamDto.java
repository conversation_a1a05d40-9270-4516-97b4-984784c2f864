package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;

import java.util.List;

/**
 * 分页查询会员入参
 *
 * <AUTHOR>
 * @date 2024/11/19 17:38
 */
@Data
public class PageMemberParamDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员真实姓名
     */
    private String realName;

    /**
     * 会员手机号
     */
    private String mobile;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 会员状态
     */
    private Integer state;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 会员生日
     */
    private String birthday;

    /**
     * 注册开始日期（必传）
     */
    private String memberRegisterBeginDate;

    /**
     * 注册结束日期（必传）
     */
    private String memberRegisterEndDate;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡状态
     */
    private Integer cardState;

    /**
     * 会员卡生效日期
     */
    private String cardEffectBeginDate;

    /**
     * 会员卡失效日期
     */
    private String cardEffectEndDate;

    /**
     * 标签ID
     */
    private List<Long> tagIds;

    /**
     * 黑名单标记 0 未拉黑 1 已拉黑
     */
    private Integer blackFlag;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * pageSize
     */
    private Integer pageSize;

}
