package com.ly.titc.pms.member.com.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.util.LocalDateUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @classname CommonUtil
 * @descrition
 * @since 2019/11/26 14:45
 */
public class CommonUtil {

    /**
     * concat key
     *
     * @param args
     * @return
     */
    public static String concat(Object... args) {

        if (null == args || args.length == 0) {
            return "";
        }
        StringJoiner sj = new StringJoiner(Constant.STRING_LINE_UNDER);
        for (Object arg : args) {
            sj.add(String.valueOf(arg));
        }
        return sj.toString();
    }

    /**
     * concat key
     *
     * @param args
     * @return
     */
    public static String concatWithDelimiter(CharSequence delimiter, Object... args) {

        if (null == args || args.length == 0) {
            return "";
        }
        StringJoiner sj = new StringJoiner(delimiter);
        for (Object arg : args) {
            sj.add(String.valueOf(arg));
        }
        return sj.toString();
    }

    /**
     * generate unique no
     *
     * @return
     */
    public static String generateUniqueNo() {
        long id = IdUtil.getSnowflake(0, 0).nextId();
        return String.valueOf(id);
    }

    public static Pair<String, String> getCardValidDate(Integer isLongTerm, Integer validPeriod) {
        String effectBeginDate;
        String effectEndDate = "";
        effectBeginDate = LocalDateUtil.formatByNormalDate(LocalDate.now());
        if (isLongTerm == 1) {
            // 长期有效
            effectEndDate = "9999-12-31";
        } else if (isLongTerm == 0 && validPeriod > 0) {
            // 非长期有效
            effectEndDate = DateUtil.offset(new Date(), DateField.DAY_OF_YEAR, validPeriod).toDateStr();
        }
        return Pair.of(effectBeginDate, effectEndDate);
    }

}
