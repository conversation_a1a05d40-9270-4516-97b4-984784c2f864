package com.ly.titc.pms.member.com.enums;


import com.ly.titc.common.ifs.IResponseCode;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @classname RespCodeEnum
 * @descrition
 * @since 2023/1/6 15:56
 */
public enum RespCodeEnum implements IResponseCode {

    /**
     * 通用
     */
    CODE_200("成功", "200"),
    CODE_400("参数非法", "400"),
    CODE_401("无操作权限", "401"),
    CODE_500("服务器异常，请稍后重试", "500"),
    CODE_1000("请求处理中，请稍后重试", "1000"),
    CODE_2000("获取锁失败", "2000"),
    CODE_3000("根据集团查询会员归属失败", "3000"),

    MEMBER_10000("会员注册请求处理中,请稍后再试!", "10000"),
    MEMBER_10001("会员注册类型非法!", "10001"),
    MEMBER_10002("会员卡已存在!", "10002"),
    MEMBER_10003("会员已经存在!", "10003"),
    MEMBER_10004("会员类型值非法!", "10004"),
    MEMBER_10005("会员等级不存在!", "10005"),
    MEMBER_10006("会员注册集团不存在!", "10006"),
    MEMBER_10007("会员注册品牌不存在!", "10007"),
    MEMBER_10008("会员注册酒店不存在!", "10008"),
    MEMBER_10009("会员注册计划不存在!", "10009"),
    MEMBER_10010("会员注册区域不存在!", "10010"),
    MEMBER_10011("会员不存在!", "10011"),
    MEMBER_10012("会员卡模式匹配!", "10012"),
    MEMBER_10013("会员卡不存在!", "10013"),
    MEMBER_10014("会员卡等级不存在!", "10014"),
    MEMBER_10015("当前会员卡已是该等级!", "10015"),
    MEMBER_10016("会员注册失败，请稍后重试", "10016"),
    MEMBER_10017("无可用会员卡号", "10017"),
    MEMBER_10018("会员卡无等级配置", "10018"),
    MEMBER_10019("会员卡目标等级不存在", "10019"),
    MEMBER_10020("存在会员拥有此会员卡，无法删除", "10020"),
    MEMBER_10021("当前会员等级存在会员使用，无法停用", "10021"),
    MEMBER_10022("会员等级必须维护价格折扣权益，否则无法保存", "10022"),
    MEMBER_10023("当前会员卡会员等级已配置，不能重复配置", "10023"),
    MEMBER_10024("当前会员卡等级升级规则，无法重复配置", "10024"),
    MEMBER_10025("当前会员卡等级保级规则，无法重复配置", "10025"),
    MEMBER_10026("会员标签已存在，请勿重复添加", "10026"),
    MEMBER_10027("会员注册发送验证码正在进行中,请稍后再试!", "10027"),
    MEMBER_10028("验证码错误，请确认验证码是否正确", "10028"),
    MEMBER_10029("会员打标失败，标签不存在", "10029"),
    MEMBER_10030("验证码过期，请重新获取", "10030"),
    MEMBER_10031("用户信息不存在", "10031"),
    MEMBER_10032("当前会员已注销，无法执行该操作", "10032"),
    MEMBER_10033("当前会员已注销，无法操作", "10033"),
    MEMBER_10034("当前会员有在住房间，无法操作", "10034"),
    MEMBER_10035("当前会员在用，无法操作", "10035"),
    MEMBER_10036("当前手机号已存在会员，无法做会员恢复", "10036"),
    MEMBER_10037("当前会员已加入黑名单，无法操作", "10037"),
    MEMBER_10038("当前会员已注销，请修改后重新提交", "10038"),
    MEMBER_10039("当前会员卡已停用，请重新选择会员卡", "10039"),
    MEMBER_10040("会员卡号已被使用，请更换卡号", "10040"),
    MEMBER_10041("不可升级为当前会员本身拥有的会员卡", "10041"),
    MEMBER_10042("当前会员等级发生变动，请确认后重新升级", "10042"),
    MEMBER_10043("仅可输入6-12位字母/数字/特殊字符组合", "10043"),
    MEMBER_10044("两次输入密码不同，请重新输入", "10044"),
    MEMBER_10045("当前卡号已被占用，无法使用，请重新输入", "10045"),
    MEMBER_10046("客户不存在", "10046"),
    MEMBER_10047("客户已绑定会员", "10047"),


    MEMBER_10049("未查询到该会员卡的售卡活动，请重新选择会员卡", "10049"),

    MEMBER_10050("未查询到该会员卡的升级活动，请重新选择会员卡", "10050"),

    MEMBER_10051("会员卡不存在或已停用", "10051"),



    CONFIG_20000("会员等级必须维护价格折扣权益，否则无法保存", "20000"),
    CONFIG_20001("会员权益不存在", "20001"),
    CONFIG_20002("会员等级必须维护价格折扣权益，否则无法保存", "20002"),
    CONFIG_20003("会员等级不存在", "20003"),
    CONFIG_20004("会员标签名称已存在", "20004"),
    CONFIG_20005("标签无法删除，已绑定会员", "20005"),
    CONFIG_20006("权益内容适用门店重复，请重新选择适用门店", "20006"),
    CONFIG_20007("权益内容适用门店重复，请重新选择适用门店", "20007"),
    CONFIG_20008("权益名称已存在，请重新输入", "20008"),
    CONFIG_20009("权益正在使用，无法删除", "20009"),
    CONFIG_20010("权益不存在", "20010"),
    CONFIG_20011("卡配置不存在", "20011"),
    CONFIG_20012("会员卡升级规则不存在", "20012"),
    CONFIG_20013("会员卡保级规则不存在", "20013"),
    CONFIG_20014("会员标签不存在", "20014"),
    CONFIG_20015("卡名称已存在", "20015"),
    CONFIG_20016("存在会员拥有此会员卡等级，无法执行操作", "20016"),

    POINT_30000("会员系统设置已存在不能重复新增", "30000"),
    POINT_30001("会员系统设置不存在，请先新增", "30001"),
    POINT_30002("积分使用规则不存在", "30002"),

    STORE_40000("储值使用规则不存在", "40000"),

    MEMBER_ORDER_00001("不支持的会员场景处理", "00001"),
    MEMBER_ORDER_00002("有相同任务在处理中，请稍后重试", "00002"),
    MEMBER_ORDER_00003("存在支付中或者支付成功的支付单,请勿重复支付", "00003"),
    MEMBER_ORDER_00004("订单不存在", "00004"),
    MEMBER_ORDER_00005("订单金额不存在", "00005"),
    MEMBER_ORDER_00006("售价类型不正确", "00006"),
    MEMBER_ORDER_00007("不支持的支付路由", "00007"),
    MEMBER_ORDER_00008("支付产品不正确", "00008"),
    MEMBER_ORDER_00009("订单已退款", "00009"),
    MEMBER_ORDER_00010("订单存在退款单，无法重复退款", "00010"),

    MEMBER_ORDER_00011("订单已退款，无法重复退款", "00011"),

    MEMBER_ORDER_00012("订单退款失败，未查询到支付成功记录", "00012"),

    MEMBER_ORDER_00013("未查询到退款单", "00013"),

    MEMBER_ORDER_00014("未查询到有效的支付单号", "00014"),

    MEMBER_ORDER_00015("退款失败，退款金额大于剩余可退金额", "00015"),

    MEMBER_ORDER_00016("活动不存在", "00016"),

    MEMBER_ORDER_00017("当前会员不符合活动参与条件", "00017"),

    NO_HOTEL_PERMISSION("无权限访问", "20001"),

    NO_HOTEL("指定酒店不存在", "20002"),

    RECYCLE_NO_BIZ("未找到指定业务id的操作记录", "20003"),

    CUSTOMER_MEMBER_EMPTY("客人编号和会员号不能都为空", "20004"),

    CUSTOMER_MEMBER_NOT_EMPTY("客人编号和会员号不能都传值", "20005"),

    ISSUE_REPEAT("该业务编码已经处理过，请勿重复操作", "20006"),
    ORDER_EXIST_NO_CANCEL("有在住或预住的订单，不能注销", "20007"),
    ISSUE_CARD_NO_CARD_ID("非默认卡需要传入cardId", "20008"),
    MOBILE_NOT_EXIST("手机号不存在", "20009"),
    MEMBER_CARD_LEVEL_CHANGED("当前卡等级小于需要降级的等级", "20010")
    ;

    private String code;
    private String desc;

    RespCodeEnum(String desc, String code) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static RespCodeEnum getByCode(String code) {
        return Arrays.stream(RespCodeEnum.values())
                .filter(item -> {
                    return item.getCode().equals(code);
                })
                .findFirst().orElse(RespCodeEnum.CODE_500);
    }
}
