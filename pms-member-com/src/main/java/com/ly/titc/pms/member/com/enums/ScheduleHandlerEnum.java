package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：ScheduleHandlerEnum
 * @Date：2024-11-21 14:50
 * @Filename：ScheduleHandlerEnum
 */
public enum ScheduleHandlerEnum {

    MEMBER_UPGRADE_RULE(1, "会员升级规则"),
    MEMBER_RELEGATION_RULE(2, "会员降级规则"),
    MEMBER_TAG(3, "会员标签"),
    MEMBER_BIRTHDAY_NOTIFY(4, "会员生日通知");

    private Integer action;

    private String desc;

    ScheduleHandlerEnum(Integer action, String desc) {
        this.action = action;
        this.desc = desc;
    }

    public Integer getAction() {
        return action;
    }

    public String getDesc() {
        return desc;
    }
}
