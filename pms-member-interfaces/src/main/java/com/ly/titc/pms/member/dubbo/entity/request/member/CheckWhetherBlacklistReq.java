package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/7/4 14:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckWhetherBlacklistReq extends BaseReq {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 会员场景
     */
    @NotBlank(message = "场景不能为空")
    private String scene;

    /**
     * 平台渠道
     */
    @NotBlank(message = "平台渠道不能为空")
    private String platformChannel;
}
