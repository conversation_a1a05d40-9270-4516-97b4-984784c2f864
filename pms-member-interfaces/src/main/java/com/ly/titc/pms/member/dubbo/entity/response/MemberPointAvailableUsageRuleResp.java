package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-15 11:54
 */
@Data
@Accessors(chain = true)
public class MemberPointAvailableUsageRuleResp {

    /**
     * 规则ID
     */
    private String usageRuleId;

    /**
     * 储值使用模式
     * 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    private Integer usageMode;

    /**
     * 使用模式值
     */
    private List<MasterObject> usageModeScopes;

    /**
     * 平台适用范围值
     */
    private List<String> scopePlatformChannels;

    /**
     * 规则名称
     */
    private String usageRuleName;

    /**
     * 积分是否可用
     */
    private Integer isCanUse;

}
