package com.ly.titc.pms.member.mediator.help;

import com.ly.titc.cc.dubbo.entity.request.log.RecordReq;
import com.ly.titc.cc.dubbo.interfaces.OperationLogDubboService;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.ActionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/7/2 20:50
 */
@Slf4j
@Component
public class MemberLogHelper {

    @DubboReference
    private OperationLogDubboService operationLogDubboService;

    /**
     * 记录日志
     *
     * @param actionEnum
     * @param blocCode
     * @param memberNo
     * @param opContent
     * @param operator
     */
    public void addLogRecord(ActionEnum actionEnum, String blocCode, String memberNo, String opContent, String operator) {
        RecordReq recordReq = new RecordReq();
        recordReq.setTenant(CommonConstant.PROJECT_CODE);
        // 操作模块
        recordReq.setBizCode(CommonConstant.MEMBER_MANAGE_CODE);
        recordReq.setBizName(CommonConstant.MEMBER_MANAGE_NAME);
        // 行为
        recordReq.setCategory(actionEnum.getCode().toString());
        recordReq.setCategoryName(actionEnum.getDesc());
        // 操作对象
        Map<String, Object> tagNamemap = new HashMap<>();
        tagNamemap.put("name", memberNo);
        tagNamemap.put("blocCode", blocCode);
        recordReq.setTags(tagNamemap);
        // 操作时间
        recordReq.setTimestamp(System.currentTimeMillis());
        // 操作内容
        recordReq.setOpContent(opContent);
        recordReq.setTrackingId(UUID.randomUUID().toString());
        // 操作用户
        recordReq.setOperator(operator);
        recordReq.setState("SUCCESS");
        Response<String> resp = operationLogDubboService.record(recordReq);
        log.info("记录日志，resp: {}", resp);
    }

}
