package com.ly.titc.pms.member.mediator.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.util.LocalDateTimeUtil;
import com.ly.titc.pms.account.dubbo.com.enums.PayChannelEnum;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ConsumeMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.entity.bo.PageMemberStoreParamBo;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ConsumeMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.MemberRecordOPResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ReceiveMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.DiscountBenefitDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberAssetMedConverter
 * @Date：2024-12-5 17:10
 * @Filename：MemberAssetMedConverter
 */
@Mapper(componentModel = "spring")
public interface MemberAssetMedConverter {


    default List<MemberRechargeOrderDto> convert(List<MemberOrderInfo> memberOrderInfoList,
                                                 List<MemberTradeConsumeRecordResp> consumeRecordRespList,
                                                 List<MemberOrderRechargeSceneInfo> sceneInfoList,
                                                 Map<String, String> hotelMap) {
        Map<String, MemberTradeConsumeRecordResp> map = consumeRecordRespList.stream().collect(Collectors.toMap(MemberTradeConsumeRecordResp::getTradeNo, Function.identity()));
        Map<String, MemberOrderRechargeSceneInfo> sceneMap = sceneInfoList.stream().collect(Collectors.toMap(MemberOrderRechargeSceneInfo::getMemberOrderNo, Function.identity()));
        return memberOrderInfoList.stream().map(memberOrderInfo -> {
            MemberRechargeOrderDto memberRechargeOrderDto = convert(memberOrderInfo);
            MemberTradeConsumeRecordResp consumeRecordResp = map.get(memberOrderInfo.getMemberOrderNo());
            MemberOrderRechargeSceneInfo sceneInfo = sceneMap.get(memberOrderInfo.getMemberOrderNo());
            memberRechargeOrderDto.setGiftExpireDate(sceneInfo.getGiftExpireDate());
            memberRechargeOrderDto.setCapitalAmount(BigDecimal.valueOf(sceneInfo.getCapitalAmount()));
            memberRechargeOrderDto.setGiftAmount(BigDecimal.valueOf(sceneInfo.getGiftAmount()));
            if (Objects.nonNull(consumeRecordResp)) {
                memberRechargeOrderDto.setConsumeAmount(consumeRecordResp.getConsumeAmount());
                memberRechargeOrderDto.setConsumeGiftAmount(consumeRecordResp.getConsumeGiftAmount());
                if(sceneInfo.getGiftExpireDate().equals(SystemConstant.PERPETUAL_EFFECT_DATE)){
                    memberRechargeOrderDto.setPerpetualEffect(true);
                }
            }
            if (memberRechargeOrderDto.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                memberRechargeOrderDto.setMasterName("集团");
            } else {
                memberRechargeOrderDto.setMasterName(hotelMap.getOrDefault(memberRechargeOrderDto.getMasterCode(), ""));
            }
            return memberRechargeOrderDto;
        }).collect(Collectors.toList());
    }

    MemberRechargeOrderDto convert(MemberOrderInfo memberOrderInfo);

    default MemberRechargeOrderDetailDto convert(MemberOrderInfo orderInfo,
                                                 MemberOrderDetailInfo detailInfo,
                                                 MemberOrderPayInfo orderPayInfo,
                                                 MemberOrderRefundInfo refundInfo,
                                                 MemberOrderRechargeSceneInfo sceneInfo){
        MemberRechargeOrderDetailDto detailDto = convertDetail(orderInfo);
        detailDto.setActivityCode(detailInfo.getActivityCode());
        detailDto.setActivityName(detailInfo.getActivityName());
        MemberOrderRechargeDto orderRechargeDto =convertRecharge(sceneInfo,orderInfo.getNum());
        detailDto.setOrderRecharge(orderRechargeDto);
        detailDto.setBenefits(new ArrayList<>());
        if(StringUtils.isNotEmpty(detailInfo.getGiftPack())){
            List<DiscountBenefitDto> benefitDtoList = JSON.parseArray(detailInfo.getGiftPack(), DiscountBenefitDto.class);
            detailDto.setBenefits(benefitDtoList);
        }
        MemberOrderPayDto orderPayDto = convert(orderPayInfo);
        if(orderPayDto ==null){
            orderPayDto = new MemberOrderPayDto();
        }
        orderPayDto.setPayChannelDesc(PayChannelEnum.getDescByCode(orderPayInfo.getPayChannel()));
        if(refundInfo != null){
            orderPayDto.setRefundTime(LocalDateTimeUtil.formatByNormalDateTime(refundInfo.getGmtModified()));
        }
        orderPayDto.setPayAmount(orderInfo.getAmount());
        if (StringUtils.isNotBlank(detailInfo.getGiftPack())) {
            List<DiscountBenefitDto> discountBenefits = JSONArray.parseArray(detailInfo.getGiftPack(), DiscountBenefitDto.class);
            detailDto.setBenefits(discountBenefits);
        }
        detailDto.setPayInfo(orderPayDto);
        return detailDto;
    }

    @Mappings({
            @Mapping(target = "payState",source = "orderPayState")
    })
    MemberOrderPayDto convert(MemberOrderPayInfo orderPayInfo);

    default MemberOrderRechargeDto convertRecharge(MemberOrderRechargeSceneInfo sceneInfo,Integer num){
        MemberOrderRechargeDto dto = convertBase(sceneInfo);
        if(dto.getGiftExpireDate().equals(SystemConstant.PERPETUAL_EFFECT_DATE)){
            dto.setPerpetualEffect(true);
        }

        dto.setPriceCapitalAmount(BigDecimal.valueOf(sceneInfo.getCapitalAmount()).divide(BigDecimal.valueOf(num),2, RoundingMode.HALF_UP));
        dto.setPriceGiftAmount(BigDecimal.valueOf(sceneInfo.getGiftAmount()).divide(BigDecimal.valueOf(num),2, RoundingMode.HALF_UP));
        return dto;
    }

    MemberOrderRechargeDto convertBase(MemberOrderRechargeSceneInfo sceneInfo);
    MemberRechargeOrderDetailDto convertDetail(MemberOrderInfo orderInfo);

    default BaseMasterReq convert(ReceiveMemberPointDto dto){
        BaseMasterReq req = new BaseMasterReq();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(dto.getBlocCode());
        req.setTrackingId(dto.getTrackingId());
        return req;
    }

    ReceiveMemberPointReq convertReceive(ReceiveMemberPointDto dto);

    MemberRecordOPResultDto convert(MemberRecordOPResultResp resultResp);

    ConsumeMemberPointReq convertConsume(ConsumeMemberPointDto dto);

    PageMemberStoreParamBo convert(PageOrderQueryDto dto);

}
