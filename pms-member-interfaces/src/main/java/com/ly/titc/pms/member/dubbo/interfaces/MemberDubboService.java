package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.member.*;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员dubbo服务
 *
 * <AUTHOR>
 * @date 2024/10/25 13:59
 */
public interface MemberDubboService {

    /**
     * 会员注册
     *
     * @param request
     * @return
     */
    Response<MemberRegisterResp> register(@Valid MemberRegisterReq request);

    /**
     * 查询会员信息
     *
     * @param request
     * @return
     */
    Response<MemberInfoResp> getByMemberNo(@Valid GetByMemberNoReq request);

    /**
     * 查询会员和会员卡信息
     *
     * @param request
     * @return
     */
    Response<MemberDetailInfoResp> getDetailByMemberNo(@Valid GetByMemberNoReq request);

    /**
     * 查询指定平台渠道和归属可用的会员信息权益和资产信息
     */
    Response<MemberDefaultCardFullInfoResp> getUsableMemberInfo(@Valid GetUsableMemberReq request);

    /**
     * 分页查询会员信息
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberDetailInfoResp>> pageMember(@Valid PageMemberReq request);

    /**
     * 根据手机号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberDetailInfoResp>> listByMobiles(@Valid ListByMobileReq request);

    /**
     * 根据手机号查询会员
     *
     * @param request
     * @return
     */
    Response<MemberInfoResp> getByMobile(@Valid GetByMobileReq request);

    /**
     * 根据证件号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberDetailInfoResp>> listByIdNos(@Valid ListByIdNoReq request);

    /**
     * 根据证件号查询会员
     *
     * @param request
     * @return
     */
    Response<MemberInfoResp> getByIdNo(@Valid GetByIdNoReq request);

    /**
     * 根据会员卡号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberDetailInfoResp>> listByCardNos(@Valid ListByCardNoReq request);

    /**
     * 根据会员号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberDetailInfoResp>> listByMemberNos(@Valid ListByMemberNoReq request);

    /**
     * 修改会员基本信息
     *
     * @param request
     * @return
     */
    Response<String> updateBaseInfo(@Valid UpdateBaseInfoReq request);

    /**
     * 会员注销
     *
     * @param request
     * @return
     */
    Response<String> cancel(@Valid DeleteMemberReq request);

    /**
     * 会员注销恢复
     *
     * @param request
     * @return
     */
    Response<String> recover(@Valid RecoverMemberReq request);

    /**
     * 修改密码
     *
     * @param request
     * @return
     */
    Response<String> changePassword(@Valid ChangePasswordReq request);

    /**
     * 根据手机号或者名称模糊匹配会员信息
     *
     * @param req
     * @return
     */
    Response<List<MemberIdentityBaseInfoResp>> fuzzyMatchMember(@Valid FuzzyMatchMemberReq req);
}
