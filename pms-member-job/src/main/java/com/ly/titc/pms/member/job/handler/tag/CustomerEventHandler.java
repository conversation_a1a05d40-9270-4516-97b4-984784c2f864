package com.ly.titc.pms.member.job.handler.tag;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.pms.member.job.converter.MemberConverter;
import com.ly.titc.pms.member.job.entity.dto.UpdateCustomDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateMemberInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.service.MemberService;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 客人事件
 * @author: luyan
 * @create: 2025-07-02 17:07
 **/
@Slf4j
@Component
public class CustomerEventHandler extends AbstractTurboMQTagHandler {

    @Resource
    private MemberConverter memberConverter;
    @Resource
    private MemberMedService memberMedService;
    @Resource
    private CustomerDecorator customerDecorator;


    @Override
    public boolean execute(String messageId, String msg) {
        UpdateCustomDto updateCustomDto = JSON.parseObject(msg, UpdateCustomDto.class);
        // 查询客人信息
        GetByCustomerNoReq req = new GetByCustomerNoReq();
        req.setBlocCode(updateCustomDto.getBlocCode());
        req.setCustomerNo(updateCustomDto.getCustomerNo());
        CustomerDetailInfoResp customerDetailInfo = customerDecorator.getDetailByCustomerNo(req);
        if (customerDetailInfo == null) {
            log.warn("未查询到客人信息，customerNo: {}", updateCustomDto.getCustomerNo());
            return true;
        }
        UpdateMemberInfoDto dto = memberConverter.convertUpdateMemberInfoDto(customerDetailInfo);
        dto.setSendMsg(false);
        memberMedService.updateMember(dto);
        return true;
    }

    @Override
    public String getTag() {
        return TurboMqTopicTag.CUSTOMER_EVENT;
    }
}
