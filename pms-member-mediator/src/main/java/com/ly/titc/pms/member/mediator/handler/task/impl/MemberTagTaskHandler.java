package com.ly.titc.pms.member.mediator.handler.task.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.biz.MemberTagConfigBiz;
import com.ly.titc.pms.member.com.enums.ScheduleHandlerEnum;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberTagConfigInfo;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConditionDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.ListMemberTagConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.MemberTagConfigDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.AddMemberProfileTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberTagDto;
import com.ly.titc.pms.member.mediator.handler.task.AbstractMemberTaskHandler;
import com.ly.titc.pms.member.mediator.service.MemberConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/2 20:39
 */
@Slf4j
@Component
public class MemberTagTaskHandler extends AbstractMemberTaskHandler {

    @Resource
    private MemberProfileMedService memberProfileMedService;

    @Resource
    private MemberConfigMedService memberConfigMedService;

    @Resource
    private MemberTagConfigBiz memberTagConfigBiz;

    @Resource
    private MemberInfoBiz memberInfoBiz;

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Override
    public Integer getTaskType() {
        return ScheduleHandlerEnum.MEMBER_TAG.getAction();
    }

    @Override
    public List<String> filterBloc() {
        return ConfigCenterUtil.listBloc();
    }

    @Override
    public Pageable<String> filterMemberNo(Integer masterType, String masterCode, Integer pageIndex, Integer pageSize) {
        // 打标
        List<MemberTagConfigInfo> memberTagConfigInfos = memberTagConfigBiz.listAutoTagRule(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberTagConfigInfos)) {
            log.info("无打标方案：masterCode: {}", masterCode);
            return Pageable.empty();
        }
        IPage<MemberInfo> page = memberInfoBiz.pageValidMembers(masterType, masterCode, pageIndex, pageSize);
        return PageableUtil.convert(page, page.getRecords().stream().map(MemberInfo::getMemberNo).collect(Collectors.toList()));
    }

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询会员的标签
        List<MemberProfileTagInfoDto> memberTags = memberProfileMedService.listMemberTag(masterType, masterCode, memberNo);
        // 查询所有的标签
        ListMemberTagConfigDto configDto = new ListMemberTagConfigDto();
        configDto.setMasterType(masterType);
        configDto.setMasterCode(masterCode);
        List<MemberTagConfigDetailDto> memberTagConfigInfos = memberConfigMedService.listTagConfig(configDto);
        memberTagConfigInfos = memberTagConfigInfos
                .stream()
                .filter(e -> e.getMarkType() == 1)
                .collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(memberTags)) {
            List<Long> existTagIds = memberTags.stream().map(MemberProfileTagInfoDto::getTagId).collect(Collectors.toList());
            memberTagConfigInfos = memberTagConfigInfos
                    .stream()
                    .filter(e -> !existTagIds.contains(e.getId()))
                    .collect(Collectors.toList());
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(memberTagConfigInfos)) {
            log.info("无自动打标的规则或用户已经存在该标签，memberNo: {}", memberNo);
            return;
        }
        List<MemberTagDto> memberTagDtos = scheduleMedConverter.convertTagDto(memberTagConfigInfos);
        for (MemberTagDto ruleDto : memberTagDtos) {
            CardConditionDto cardConditionDto = new CardConditionDto();
            cardConditionDto.setMasterType(masterType);
            cardConditionDto.setMasterCode(masterCode);
            cardConditionDto.setMemberNo(memberNo);
            cardConditionDto.setCycleType(ruleDto.getCycleType());
            cardConditionDto.setRelationType(ruleDto.getSatisfyPerformType());
            cardConditionDto.setDetails(ruleDto.getMarkRules());
            ConditionCheckResult checkResult = conditionCheck(cardConditionDto);
            log.info("打标结果，checkResult: {}, memberNo: {}", checkResult, memberNo);
            if (checkResult.isPassed()) {
                // 打标
                AddMemberProfileTagDto addMemberProfileTagDto = new AddMemberProfileTagDto();
                addMemberProfileTagDto.setMasterType(ruleDto.getMasterType());
                addMemberProfileTagDto.setMasterCode(ruleDto.getMasterCode());
                addMemberProfileTagDto.setTagId(ruleDto.getId());
                addMemberProfileTagDto.setTagName(ruleDto.getName());
                addMemberProfileTagDto.setMarkType(2);
                addMemberProfileTagDto.setCreateUser("自动打标");
                addMemberProfileTagDto.setTagType(ruleDto.getType());
                addMemberProfileTagDto.setMemberNo(memberNo);
                memberProfileMedService.addMemberTag(addMemberProfileTagDto);
                // 记录操作日志
                log.info("会员标签添加成功 - 会员编号: {}, 标签名称: {}, 原因: {}",
                        memberNo, ruleDto.getName(), checkResult.generateReason());
            } else {
                if (ruleDto.getAutoDelete().equals(1)) {
                    // 不满足条件，删除 TODO 删除标签
                    memberProfileMedService.deleteMemberTag(ruleDto.getMasterType(), ruleDto.getMasterCode(), memberNo, ruleDto.getId(), "SYSTEM");
                    // 记录操作日志
                    log.info("会员标签删除成功 - 会员编号: {}, 标签名称: {}, 原因: {}",
                            memberNo, ruleDto.getName(), checkResult.generateReason());
                }
            }
        }
    }
}
