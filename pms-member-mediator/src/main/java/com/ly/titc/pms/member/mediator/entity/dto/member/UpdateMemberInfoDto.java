package com.ly.titc.pms.member.mediator.entity.dto.member;

import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import lombok.Data;

/**
 * 更新会员信息
 *
 * <AUTHOR>
 * @date 2024/11/5 19:20
 */
@Data
public class UpdateMemberInfoDto {

    /**
     * 会员基础信息
     */
    private MemberInfo memberInfo;

    /**
     * 会员拓展信息
     */
    private MemberExtendInfo memberExtendInfo;

    /**
     * 会员联系信息
     */
    private MemberContactInfo memberContactInfo;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否推送消息
     */
    private boolean sendMsg = true;

}
