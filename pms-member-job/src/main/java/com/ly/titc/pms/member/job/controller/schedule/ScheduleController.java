package com.ly.titc.pms.member.job.controller.schedule;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.job.entity.request.ExecuteScheduleRequest;
import com.ly.titc.pms.member.mediator.handler.task.AbstractMemberTaskHandler;
import com.ly.titc.pms.member.mediator.handler.task.MemberTaskHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author：rui
 * @name：ScheduleController
 * @Date：2024-11-22 16:39
 * @Filename：ScheduleController
 */
@RestController
@RequestMapping("/schedule")
@Slf4j
public class ScheduleController {

    @Resource
    private RedisFactory redisFactory;

//    /**
//     * 定时任务执行 升降机、标签打标
//     *
//     * @param request
//     */
//    @RequestMapping("/execute/member")
//    public void execute(@RequestBody @Valid ExecuteScheduleRequest request) {
//        AbstractScheduleHandler handler = ScheduleHandlerFactory.getHandler(request.getScheduleType());
//        handler.doSchedule();
//    }

    /**
     * 批量执行会员任务
     *
     * @param request
     */
    @PostMapping("/doMemberTask")
    public void doMemberTask(@RequestBody @Valid ExecuteScheduleRequest request){
        AbstractMemberTaskHandler handler = MemberTaskHandlerFactory.getHandler(request.getScheduleType());
        List<String> blocCodes = handler.filterBloc();
        if (CollectionUtils.isEmpty(blocCodes)) {
            return;
        }
        for (String blocCode : blocCodes) {
            log.info("开始执行任务：{}", blocCode);
            String lockKey = handler.getLockKey(blocCode);
            Boolean lock = redisFactory.setNx(lockKey, Constant.SECONDS_BY_FIVE_MINUTES, "1");
            if (lock) {
                log.info("任务执行失败");
                continue;
            }
            try {
                TwoTuple<Integer, String> memberMaster = ConfigCenterUtil.getMasterByBloc(blocCode);
                int pageIndex = 1;
                while (true) {
                    Integer masterType = memberMaster.getFirst();
                    String masterCode = memberMaster.getSecond();
                    Pageable<String> pageable = handler.filterMemberNo(masterType, masterCode, pageIndex, Constant.THOUSAND);
                    if (CollectionUtils.isEmpty(pageable.getDatas())) {
                        return;
                    }
                    handler.sendTaskMq(masterType, masterCode, pageable.getDatas());
                    Thread.sleep(200);
                    pageIndex++;
                }
            } catch (Exception e) {
                log.error("批量发送会员定时任务失败, blocCode:{}", blocCode, e);
            } finally {
                redisFactory.del(lockKey);
            }
        }
    }
}
