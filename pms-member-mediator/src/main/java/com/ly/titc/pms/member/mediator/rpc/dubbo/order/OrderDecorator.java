package com.ly.titc.pms.member.mediator.rpc.dubbo.order;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.order.dubbo.entity.request.CheckMemberActiveBookingReq;
import com.ly.titc.pms.order.dubbo.interfaces.OrderQueryDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: luyan
 * @create: 2025-07-03 15:38
 **/
@Slf4j
@Component
public class OrderDecorator {

    @DubboReference(group = "${order-dsf-group}", check = false)
    private OrderQueryDubboService orderQueryDubboService;

    /**
     * 检查会员是否有有效的订单：预定或者在住的订单
     * @param blocCode
     * @param memberNo
     * @return
     */
    public Boolean checkMemberActiveBooking(String blocCode, String memberNo) {
        CheckMemberActiveBookingReq req = new CheckMemberActiveBookingReq();
        req.setBlocCode(blocCode);
        req.setMemberNo(memberNo);
        return Response.getValidateData(orderQueryDubboService.checkMemberActiveBooking(req));
    }
}
