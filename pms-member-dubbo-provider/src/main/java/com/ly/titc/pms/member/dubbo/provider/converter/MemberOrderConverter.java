package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.entity.request.member.IssueMemberCardReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.*;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import com.ly.titc.pms.member.dubbo.entity.response.CreateOrderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayOrderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayUnifiedOrderResp;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpgradeMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2025/2/8 18:07
 */
@Mapper(componentModel = "spring")
public interface MemberOrderConverter {

    default CreateOrderDto<RegisterMemberDto> convert(CreateRegisterOrderReq request) {
        CreateOrderDto<RegisterMemberDto> orderDto = convertBase(request, request.getMasterType(), request.getMasterCode());
        IssueMemberCardReq cardRequest = request.getRegisterRequest().getMemberCardInfo();
        orderDto.setMemberScene(MemberSceneEnum.REGISTER.getScene());
        orderDto.setMemberSceneDesc(String.format("注册（%s-%s）", cardRequest.getCardName(), cardRequest.getCardLevelName()));
        TwoTuple<Integer, String> masterByBloc = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        RegisterMemberDto convert = convert(request.getBlocCode(), request.getRegisterRequest(), masterByBloc.getFirst(), masterByBloc.getSecond(), request.getOperator());
        convert.setSource(request.getPlatformChannel());
        orderDto.setMemberSceneNoteDto(convert);
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.MEMBER_CARD_SALE.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(request.getPlatformChannel());
        orderDto.setMemberCardNo(cardRequest.getMemberCardNo());
        return orderDto;
    }

    default CreateOrderDto<PurchaseCardDto> convert(CreatePurchaseCardOrderReq request) {
        CreateOrderDto<PurchaseCardDto> orderDto = convertBase(request, request.getMasterType(), request.getMasterCode());
        orderDto.setMemberScene(MemberSceneEnum.PURCHASECARD.getScene());
        orderDto.setMemberSceneDesc(String.format("售卡（%s-%s）", request.getCardName(), request.getCardLevelName()));
        orderDto.setMemberSceneNoteDto(convertPurchaseCardDto(request, request.getMasterType(), request.getMasterCode(), request.getOperator()));
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.MEMBER_CARD_SALE.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(request.getPlatformChannel());
        orderDto.setMemberCardNo(request.getMemberCardNo());
        return orderDto;
    }

    default CreateOrderDto<UpgradeMemberDto> convert(CreateUpgradeOrderReq request) {
        CreateOrderDto<UpgradeMemberDto> orderDto = convertBase(request, request.getMasterType(), request.getMasterCode());
        orderDto.setMemberScene(MemberSceneEnum.UPGRADE.getScene());
        orderDto.setMemberSceneDesc(String.format("升级（%s-%s升级至%s）", request.getCardName(), request.getPreLevelName(), request.getAfterLevelName()));
        orderDto.setMemberSceneNoteDto(convertUpgradeMemberDto(request, request.getMasterType(), request.getMasterCode(), request.getOperator(), ChangeTypeEnum.UPGRADE_PURCHASE.getType()));
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.MEMBER_PAID_UPGRADE.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(request.getPlatformChannel());
        orderDto.setMemberCardNo(request.getMemberCardNo());
        orderDto.setMemberNo(request.getMemberNo());
        return orderDto;
    }

    default CreateOrderDto<MemberStoreRechargeDto> convert(CreateStoreOrderReq request){
        CreateOrderDto<MemberStoreRechargeDto> orderDto = convertBase(request, request.getMasterType(), request.getMasterCode());
        MemberRechargeReq rechargeRequest = request.getRechargeRequest();
        orderDto.setMemberScene(MemberSceneEnum.RECHARGE.getScene());
        orderDto.setMemberSceneDesc(String.format("%s-%s", "充值", rechargeRequest.getCapitalAmount().add(rechargeRequest.getGiftAmount())));
        MemberStoreRechargeDto rechargeDto = convertDto(rechargeRequest,request);
        rechargeDto.setPlatformChannel(request.getPlatformChannel());
        rechargeDto.setMasterType(request.getMasterType());
        rechargeDto.setMasterCode(request.getMasterCode());
        rechargeRequest.setPlatformChannel(request.getPlatformChannel());
        orderDto.setMemberSceneNoteDto(rechargeDto);
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.STORE_GIFT.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(request.getPlatformChannel());
        orderDto.setMemberNo(request.getMemberNo());
        return orderDto;
    }

    @Mappings({
            @Mapping(target = "memberNo", source = "request.memberNo"),
            @Mapping(target = "blocCode", source = "request.blocCode"),
            @Mapping(target = "clubCode", source = "request.clubCode"),
            @Mapping(target = "hotelCode", source = "request.hotelCode"),
            @Mapping(target = "operator", source = "request.operator"),
            @Mapping(target = "platformChannel", source = "request.platformChannel")
    })
    MemberStoreRechargeDto convertDto(MemberRechargeReq rechargeRequest, CreateStoreOrderReq request);

    MemberPayOrderDto convert(MemberPayOrderReq request);
    @Mappings({
            @Mapping(target = "memberOrderPayNo", source = "memberOrderPayTradeNo")
    })
    GetPayStateDto convert(GetPayStateReq request);

    ActivityOrderDto convertActivity(CreateOrderBaseReq request);

    CreateOrderDto<RegisterMemberDto> convertBase(CreateRegisterOrderReq request, Integer masterType, String masterCode);

    CreateOrderDto<PurchaseCardDto> convertBase(CreatePurchaseCardOrderReq request, Integer masterType, String masterCode);

    CreateOrderDto<UpgradeMemberDto> convertBase(CreateUpgradeOrderReq request, Integer masterType, String masterCode);

    CreateOrderDto<MemberStoreRechargeDto> convertBase(CreateStoreOrderReq request, Integer masterType, String masterCode);


    @Mappings({
            @Mapping(target = "blocCode", source = "blocCode"),
            @Mapping(target = "operator", source = "operator")
    })
    RegisterMemberDto convert(String blocCode, MemberRegisterReq registerRequest, Integer masterType, String masterCode, String operator);

    @Mappings({
            @Mapping(target = "operator", source = "operator")
    })
    PurchaseCardDto convertPurchaseCardDto(CreatePurchaseCardOrderReq registerRequest, Integer masterType, String masterCode, String operator);

    @Mappings({
            @Mapping(target = "operator", source = "operator")
    })
    UpgradeMemberDto convertUpgradeMemberDto(CreateUpgradeOrderReq registerRequest, Integer masterType, String masterCode,  String operator, Integer changeType);

    CreateOrderResp convert(CreateOrderResultDto resultDto);

    @Mappings({
            @Mapping(target = "memberOrderPayTradeNo", source = "memberOrderPayNo"),
            @Mapping(target = "tradeState", source = "payState"),
    })
    MemberPayOrderResp convert(PayOrderResultDto resultDto);

    RefundOrderDto convert(RefundOrderReq request, Integer masterType, String masterCode);
    @Mappings({
            @Mapping(target = "memberOrderPayTradeNo", source = "refundPayNo"),
            @Mapping(target = "tradeState", source = "refundState"),
    })
    CreateRefundOrderResp convert(RefundResultDto refundResultDto);
    @Mappings({
            @Mapping(target = "refundPayNo", source = "memberOrderPayTradeNo"),
    })
    GetRefundStateDto convert(GetRefundStateReq request);

    MemberPayUnifiedOrderResp convertUnified(MemberPayUnifiedOrderDto resultDto);
}
