package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/26 20:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListPointRuleReq extends BaseReq {

    /**
     * 使用模式主体类型
     */
    private String scopeSource;

    /**
     * 使用模式主体类型编码
     */
    private String scopeSourceCode;

    /**
     * 适用平台
     */
    private String platformChannel;

}
