package com.ly.titc.pms.member.mediator.handler.task.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.ScheduleHandlerEnum;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.mediator.entity.dto.member.PageMemberParamDto;
import com.ly.titc.pms.member.mediator.handler.task.AbstractMemberTaskHandler;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_EVENT;

/**
 * <AUTHOR>
 * @date 2025/7/2 21:11
 */
@Slf4j
@Component
public class MemberBirthdayNotifyTaskHandler extends AbstractMemberTaskHandler {

    @Resource
    private MemberMedService memberMedService;

    @Override
    public Integer getTaskType() {
        return ScheduleHandlerEnum.MEMBER_BIRTHDAY_NOTIFY.getAction();
    }

    @Override
    public List<String> filterBloc() {
        return ConfigCenterUtil.listBloc();
    }

    @Override
    public Pageable<String> filterMemberNo(Integer masterType, String masterCode, Integer pageIndex, Integer pageSize) {
        PageMemberParamDto paramDto = new PageMemberParamDto();
        paramDto.setMasterType(masterType);
        paramDto.setMasterCode(masterCode);
        paramDto.setState(StateEnum.VALID.getState());
        paramDto.setMemberRegisterBeginDate("2023-01-01");
        paramDto.setMemberRegisterEndDate(LocalDate.now().toString());
        paramDto.setBirthday(LocalDate.now().toString());
        paramDto.setPageIndex(pageIndex);
        paramDto.setPageSize(pageSize);
        return memberMedService.pageMemberNoByFinalMode(paramDto);
    }

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {

    }

    @Override
    public void sendTaskMq(Integer masterType, String masterCode, List<String> memberNos) {
        // 发送消息执行任务
        for (String memberNo : memberNos) {
            MemberEventMsg memberEventMsg = new MemberEventMsg();
            memberEventMsg.setMasterType(masterType).setMasterCode(masterCode)
                    .setMemberNo(memberNo).setEventType(MemberEventEnum.CANCEL_BLACKLIST);
            String msg = JSONObject.toJSONString(memberEventMsg);
            try {
                SendResult sendResult = producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_EVENT, msg);
                log.info("发送生日会员成功，masterType:{}, masterCode:{}, taskType:{}, sendResult:{}", masterType, masterCode, getTaskType(), sendResult);
            } catch (Exception e) {
                log.error("发送生日会员任务失败, masterType:{}, masterCode:{}, taskType:{}, msg:{}",  masterType, masterCode, getTaskType(), memberNos, e);
            }
        }

    }
}
