package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCardInfoDto
 * @Date：2024-11-18 17:58
 * @Filename：MemberCardInfoDto
 */
@Data
@Accessors(chain = true)
public class UpdateMemberCardDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 操作人
     */
    private String operator;

}
