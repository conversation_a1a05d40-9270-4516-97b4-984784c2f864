package com.ly.titc.pms.member.mediator.handler.task;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.entity.BaseCheck;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.CycleTypeEnum;
import com.ly.titc.pms.member.com.enums.SuccessfulPerformTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConditionDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberScheduleMqDto;
import com.ly.titc.pms.member.mediator.help.MemberLogHelper;
import com.ly.titc.pms.member.mediator.help.MemberTaskCacheHelper;
import com.ly.titc.pms.member.mediator.help.MemberTaskCheckConditionHelper;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_SCHEDULE;

/**
 * 会员任务执行
 *
 * <AUTHOR>
 * @date 2025/7/2 19:50
 */
@Slf4j
public abstract class AbstractMemberTaskHandler {

    @Resource
    protected MemberTaskCheckConditionHelper memberTaskCheckConditionHelper;
    @Resource
    protected MemberTaskCacheHelper memberTaskCacheHelper;
    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;
    @Resource(type = TurboMQProducer.class)
    protected TurboMQProducer producer;

    /**
     * 获取任务类型
     *
     * @return
     */
    public abstract Integer getTaskType();

    /**
     * 筛选集团
     *
     * @return
     */
    public abstract List<String> filterBloc();

    /**
     * 获取锁KEY
     *
     * @param blocCode
     * @return
     */
    public String getLockKey(String blocCode){
        return "MEMBER_TASK_" + getTaskType() + "_" + blocCode;
    }

    /**
     * 筛选符合的会员
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    public abstract Pageable<String> filterMemberNo(Integer masterType, String masterCode, Integer pageIndex, Integer pageSize);

    /**
     * 执行处理
     *
     * @param masterType
     * @param masterCode
     * @param memberNo
     */
    public abstract void process(Integer masterType, String masterCode, String memberNo);


    protected ConditionCheckResult conditionCheck(CardConditionDto cardConditionDto) {
        Integer masterType = cardConditionDto.getMasterType();
        String masterCode = cardConditionDto.getMasterCode();
        String memberNo = cardConditionDto.getMemberNo();
        Integer cycleType = cardConditionDto.getCycleType();
        String relationType = cardConditionDto.getRelationType();
        List<? extends ConditionCheckResult.ConditionDetail> details = cardConditionDto.getDetails();
        // 根据统计周期确定开始时间
        MemberCardLevelChangeRecord memberCardLevelChangeRecord;
        LocalDateTime startDate;
        LocalDateTime endDate = LocalDateTime.now();
        if (cycleType.equals(CycleTypeEnum.SINCE_REGISTER.getType())) {
            // 获取这个会员的注册日期
            memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                    Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
            startDate = memberCardLevelChangeRecord.getGmtCreate();
        } else {
            // 获取这个会员的上次升降级日期
            memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                    Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(),
                            ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(), ChangeTypeEnum.SUCCESS.getType()));
            startDate = memberCardLevelChangeRecord.getGmtCreate();
        }
        log.info("memberNo: {}, startDate: {}", memberNo, startDate);
        // 获取会员的各种记录数据（使用缓存优化，避免重复查询）
        MemberRecordsCache cache = new MemberRecordsCache();
        Map<String, List<BaseCheck>> memberRecords = memberTaskCacheHelper.getAllMemberRecords(masterType, masterCode, memberNo, startDate, endDate, cache);
        // 检查保级条件
        ConditionCheckResult checkResult;
        if (StringUtils.equals(relationType, SuccessfulPerformTypeEnum.ALL.getType())) {
            checkResult = memberTaskCheckConditionHelper.checkAllConditionsWithDetail(details, memberNo,
                    memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                    memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                    memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
        } else {
            checkResult = memberTaskCheckConditionHelper.checkAnyConditionWithDetail(details, memberNo,
                    memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                    memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                    memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
        }
        log.info("memberNo: {}, checkResult: {}", memberNo, checkResult);
        return checkResult;
    }

    /**
     * 发送任务消息
     *
     * @param masterType
     * @param masterCode
     * @param memberNos
     */
    public void sendTaskMq(Integer masterType, String masterCode, List<String> memberNos) {
        // 发送消息执行任务
        MemberScheduleMqDto msg = new MemberScheduleMqDto();
        msg.setMemberNo(memberNos).setMasterCode(masterCode)
                .setMasterType(masterType).setAction(getTaskType());
        String str = JSONObject.toJSONString(msg);
        try {
            SendResult sendResult = producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            log.info("批量发送会员定时任务成功，masterType:{}, masterCode:{}, taskType:{}, sendResult:{}", masterType, masterCode, getTaskType(), sendResult);
        } catch (Exception e) {
            log.error("批量发送会员定时任务失败, masterType:{}, masterCode:{}, taskType:{}, msg:{}",  masterType, masterCode, getTaskType(), memberNos, e);
        }
    }
}
