package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.cashier.dubbo.entity.request.CashierRefundApplyReq;
import com.ly.titc.cashier.dubbo.entity.request.CashierRefundInfoGetReq;
import com.ly.titc.cashier.dubbo.entity.request.trade.CashierAccountSettleDeductTradeReq;
import com.ly.titc.cashier.dubbo.entity.request.trade.CashierAccountSettleRefundGetStateReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundDetailResp;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundResp;
import com.ly.titc.cashier.dubbo.entity.response.trade.CashierAccountSettleDeductResp;
import com.ly.titc.pms.account.dubbo.com.enums.SourceSystemEnum;
import com.ly.titc.pms.member.com.enums.OrderRefundStateEnum;
import com.ly.titc.pms.member.com.utils.GenerateUtils;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import org.mapstruct.*;


/**
 * 退款单Converter
 *
 * <AUTHOR>
 * @date 2024/12/12 13:42
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        builder = @Builder(disableBuilder = true))
public interface MemberRefundOrderMedConverter {

    default MemberOrderRefundInfo convertRefundOrderInfo(MemberOrderInfo memberOrderInfo,
                                                         MemberOrderPayInfo memberOrderPayInfo,
                                                         RefundOrderDto dto){
        MemberOrderRefundInfo memberOrderRefundInfo = new MemberOrderRefundInfo();
        memberOrderRefundInfo.setClubCode(memberOrderInfo.getClubCode());
        memberOrderRefundInfo.setBlocCode(memberOrderInfo.getBlocCode());
        memberOrderRefundInfo.setHotelCode(memberOrderInfo.getHotelCode());
        memberOrderRefundInfo.setMemberRefundNo(GenerateUtils.generateRefundOrderNo());
        memberOrderRefundInfo.setMemberOrderNo(memberOrderInfo.getMemberOrderNo());
        memberOrderRefundInfo.setPlatformChannel(dto.getPlatformChannel());
        memberOrderRefundInfo.setMemberNo(memberOrderInfo.getMemberNo());
        memberOrderRefundInfo.setMasterType(memberOrderInfo.getMasterType());
        memberOrderRefundInfo.setMasterCode(memberOrderInfo.getMasterCode());
        memberOrderRefundInfo.setMemberScene(memberOrderInfo.getMemberScene());
        memberOrderRefundInfo.setRefundState(OrderRefundStateEnum.REFUNDING.getState());
        memberOrderRefundInfo.setRefundAmount(dto.getRefundAmount());
        memberOrderRefundInfo.setReason(dto.getReason());
        memberOrderRefundInfo.setCreateUser(dto.getOperator());
        memberOrderRefundInfo.setModifyUser(dto.getOperator());
        memberOrderRefundInfo.setTermId(dto.getTermId());
        memberOrderRefundInfo.setMemberOrderPayNo(memberOrderPayInfo.getMemberOrderPayNo());
        return memberOrderRefundInfo;
    }

    default CashierRefundApplyReq convertRefundReq(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo){
        CashierRefundApplyReq req = new CashierRefundApplyReq();
        req.setSourceSystem(SourceSystemEnum.MEMBER.getCode());
        req.setBizRefundNo(processRefundOrder.getMemberRefundNo());
        req.setTransactionId(memberOrderPayInfo.getTransactionId());
        req.setOnlinePayNo(memberOrderPayInfo.getOnlinePayNo());
        req.setReason(processRefundOrder.getReason());
        req.setTermId(processRefundOrder.getTermId());
        req.setBlocCode(processRefundOrder.getBlocCode());
        req.setHotelCode(processRefundOrder.getHotelCode());
        req.setOperator(processRefundOrder.getCreateUser());
        req.setAmount(processRefundOrder.getRefundAmount());
        return req;
    }
    
    

    @Mappings({
            @Mapping(target = "refundPayNo",source = "onlineRefundPayNo"),
            @Mapping(target = "refundTransactionId",source = "transactionId")
    })
    RefundResultDto convertRefundResultDto(MemberOrderRefundInfo processRefundOrder);

    @Mappings({
            @Mapping(target = "refundPayNo",source = "memberOrderRefundInfo.onlineRefundPayNo")
    })
    CashierRefundInfoGetReq convertRefundDetailReq(MemberOrderRefundInfo memberOrderRefundInfo, String operator);

    CashierRefundResp convertRefundResultResp(CashierRefundDetailResp refundResult);

    OrderRefundResultDto convert(CashierRefundResp refundResp);

    @Mappings({
            @Mapping(target = "blocCode",source = "processRefundOrder.blocCode"),
            @Mapping(target = "hotelCode",source = "processRefundOrder.hotelCode"),
            @Mapping(target = "originSettlePaymentTradeNo",source = "memberOrderPayInfo.onlinePayNo"),
            @Mapping(target = "bizRefundNo",source = "processRefundOrder.memberRefundNo"),
            @Mapping(target = "sourceSystem",constant = "MEMBER"),
            @Mapping(target = "operator",source = "processRefundOrder.createUser"),
            @Mapping(target = "amount",source = "processRefundOrder.refundAmount"),
            @Mapping(target = "originAmount",source = "memberOrderPayInfo.amount"),
            @Mapping(target = "remark",source = "processRefundOrder.reason"),

    })
    CashierAccountSettleDeductTradeReq convertDeductReq(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo);
    @Mappings({
            @Mapping(target = "refundPayNo",source = "settleOrderTradeNo"),
            @Mapping(target = "refundTransactionId",source = "resp.paymentInfoDto.transactionId"),
            @Mapping(target = "refundState",source = "tradeState"),
            @Mapping(target = "failReason",source = "tradeFailReason"),

    })
    OrderRefundResultDto convertResult(CashierAccountSettleDeductResp resp);

    CashierAccountSettleRefundGetStateReq convertGetRefundState(MemberOrderRefundInfo memberOrderRefundInfo, String operator);
}
