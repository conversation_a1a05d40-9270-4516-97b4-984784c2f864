package com.ly.titc.pms.member.dubbo.entity.request.member;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 手动回收会员卡请求
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
public class RecycleMemberCardManualRequest {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 集团编码
     */
    private String blocCode;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    /**
     * 业务id(必传，用于查询之前的下发操作)
     */
    @NotBlank(message = "业务ID不能为空")
    private String bizNo;

    /**
     * 回收原因
     */
    private String reason;
}
