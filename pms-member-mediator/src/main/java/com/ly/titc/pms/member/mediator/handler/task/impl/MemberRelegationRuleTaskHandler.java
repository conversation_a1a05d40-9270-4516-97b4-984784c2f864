package com.ly.titc.pms.member.mediator.handler.task.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.biz.CardLevelConfigBiz;
import com.ly.titc.pms.member.biz.CardLevelRelegationRuleBiz;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.ScheduleHandlerEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDto;
import com.ly.titc.pms.member.mediator.handler.task.AbstractMemberTaskHandler;
import com.ly.titc.pms.member.mediator.handler.task.MemberRecordsCache;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/7/2 20:40
 */
@Slf4j
@Component
public class MemberRelegationRuleTaskHandler extends AbstractMemberTaskHandler {

    @Resource
    private CardLevelRelegationRuleBiz cardLevelRelegationRuleBiz;

    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Resource
    private MemberRecordsCache memberRecordsCache;

    @Override
    public Integer getTaskType() {
        return ScheduleHandlerEnum.MEMBER_RELEGATION_RULE.getAction();
    }

    @Override
    public List<String> filterBloc() {
        return ConfigCenterUtil.listBloc();
    }

    @Override
    public Pageable<String> filterMemberNo(Integer masterType, String masterCode, Integer pageIndex, Integer pageSize) {
        List<CardLevelRelegationRuleInfo> relationRuleList = cardLevelRelegationRuleBiz.list(masterType, masterCode);
        if (CollectionUtils.isEmpty(relationRuleList)) {
            log.info("无降级方案：masterCode: {}", masterCode);
            return Pageable.empty();
        }
        String today = LocalDate.now().toString();
        IPage<MemberCardInfo> page = memberCardInfoBiz.pageRelegationCheckCards(masterType, masterCode, pageIndex, pageSize, today);
        return PageableUtil.convert(page, page.getRecords().stream().map(MemberCardInfo::getMemberNo).distinct().collect(Collectors.toList()));
    }

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }
        // 查询每个卡对应的规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelRelegationRuleDto> rules = cardConfigMedService.listCardLevelRelegationRule(relegationRuleDto);
        // 按照sort排序，同一个cardId-sourceLevel组合的规则按sort顺序执行
        Map<String, List<CardLevelRelegationRuleDto>> ruleMap = rules.stream()
                .sorted(Comparator.comparing(CardLevelRelegationRuleDto::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.groupingBy(
                        item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()),
                        Collectors.toList()
                ));
        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));
        // 使用Spring注入的Redis缓存对象，避免重复查询
        LocalDateTime endDate = LocalDateTime.now();
        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            List<CardLevelRelegationRuleDto> rulesForCard = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的保级规则，跳过这张卡
            if (CollectionUtils.isEmpty(rulesForCard)) {
                log.info("无对应的保级规则，memberNo: {}, cardId: {}, cardLevel: {}", memberNo, cardId, cardLevel);
                continue;
            }
            // 按sort顺序执行所有规则
            for (CardLevelRelegationRuleDto rule : rulesForCard) {
                MemberRelegationRuleDto ruleDto = scheduleMedConverter.convertRelegationRuleDto(rule);
                if (ruleDto == null) {
                    continue;
                }
                CardConditionDto cardConditionDto = new CardConditionDto();
                cardConditionDto.setMasterType(masterType);
                cardConditionDto.setMasterCode(masterCode);
                cardConditionDto.setMemberNo(memberNo);
                cardConditionDto.setCycleType(ruleDto.getCycleType());
                cardConditionDto.setRelationType(ruleDto.getRelegationSuccessfulPerformType());
                cardConditionDto.setDetails(ruleDto.getDetails());
                ConditionCheckResult checkResult = conditionCheck(cardConditionDto);
                // 对于降级规则，需要反转结果：如果条件满足则保级成功，如果条件不满足则降级
                boolean shouldDowngrade = !checkResult.isPassed();
                CardLevelConfigWithPrivilegeDto currentLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
                if (currentLevelInfo == null) {
                    continue;
                }
                if (shouldDowngrade) {
                    // 保级失败，执行降级 - 计算降级目标等级
                    Integer targetLevel = calculateDowngradeTargetLevel(memberNo, memberCardInfo.getMemberCardNo(), cardLevel);
                    log.info("保级失败，执行降级, memberNo: {}, targetLevel: {}", memberNo, targetLevel);
                    // 如果已经是1级或者计算出的目标等级无效，则不降级
                    if (targetLevel == null || targetLevel >= cardLevel) {
                        log.info("会员已经是1级或者计算出的目标等级无效，memberNo: {}, targetLevel: {}", memberNo, targetLevel);
                        continue;
                    }
                    CardLevelConfigInfo cardLevelConfig = cardLevelConfigBiz.getByCardLevel(cardId, targetLevel);
                    if (cardLevelConfig == null) {
                        log.info("需要降级的会员卡等级不存在，memberNo: {}, targetLevel: {}", memberNo, targetLevel);
                        continue;
                    }
                    Pair<String, String> pair = CommonUtil.getCardValidDate(cardLevelConfig.getIsLongTerm(), cardLevelConfig.getValidPeriod());
                    // 降级会员卡等级
                    UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                    updateCardLevelDto.setMemberNo(memberNo)
                            .setCardId(cardId)
                            .setMemberCardNo(memberCardInfo.getMemberCardNo())
                            .setPreLevel(cardLevel)
                            .setPreLevelName(currentLevelInfo.getCardLevelName())
                            .setAfterLevel(targetLevel)
                            .setAfterLevelName(cardLevelConfig.getCardLevelName())
                            .setEffectBeginDate(pair.getLeft())
                            .setEffectEndDate(pair.getRight())
                            .setIsLongTerm(cardLevelConfig.getIsLongTerm())
                            .setChangeType(ChangeTypeEnum.DOWN_AUTO.getType())
                            .setReason("自动降级：" + checkResult.generateReason())
                            .setOperator("自动降级，规则名称：" + ruleDto.getName())
                            .setRuleId(rule.getId())
                            .setIsLongTerm(cardLevelConfig.getIsLongTerm())
                            .setSourceType("BLOC")
                            .setSourceHotel(masterCode);
                    memberCardMedService.updateCardLevel(updateCardLevelDto);
                } else {
                    Pair<String, String> pair = CommonUtil.getCardValidDate(currentLevelInfo.getIsLongTerm(), currentLevelInfo.getValidPeriod());
                    // 保级成功，记录保级成功信息
                    String successReason = "保级成功：" + checkResult.generateReason();
                    MemberCardLevelChangeRecord changeRecord = new MemberCardLevelChangeRecord();
                    changeRecord.setMemberNo(memberNo)
                            .setCardId(cardId)
                            .setMemberCardNo(memberCardInfo.getMemberCardNo())
                            .setPreLevel(cardLevel)
                            .setPreLevelName(currentLevelInfo.getCardLevelName())
                            .setAfterLevel(cardLevel) // 保级成功，等级不变
                            .setAfterLevelName(currentLevelInfo.getCardLevelName())
                            .setEffectBeginDate(pair.getLeft())
                            .setEffectEndDate(pair.getRight())
                            .setChangeType(ChangeTypeEnum.SUCCESS.getType())
                            .setReason(successReason)
                            .setRuleId(rule.getId())
                            .setSourceType("BLOC")
                            .setSourceHotel(masterCode);
                    memberCardLevelChangeRecordBiz.add(changeRecord);
                }
            }
        }
    }


    /**
     * 计算降级目标等级
     * 逻辑：降级到升级之前的等级，也就是查询member_card_level_change_record表获取之前的等级，
     * 如果查询不到就减1，但如果已经是1了，就不降级
     */
    private Integer calculateDowngradeTargetLevel(String memberNo, String memberCardNo, Integer currentLevel) {
        // 如果当前等级已经是1，不能再降级
        if (currentLevel <= 1) {
            return null;
        }
        // 查询该会员卡的最近一次升级记录
        MemberCardLevelChangeRecord lastUpgradeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                Arrays.asList(ChangeTypeEnum.UPGRADE_AUTO.getType(), ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(),
                        ChangeTypeEnum.UPGRADE_PURCHASE.getType()));
        // 如果找到了升级记录，且升级后的等级是当前等级，则降级到升级前的等级
        if (lastUpgradeRecord != null && lastUpgradeRecord.getMemberCardNo().equals(memberCardNo)
                && lastUpgradeRecord.getAfterLevel().equals(currentLevel)) {
            return lastUpgradeRecord.getPreLevel();
        }
        // 如果没有找到升级记录，则降级到当前等级-1
        return currentLevel - 1;
    }
}
