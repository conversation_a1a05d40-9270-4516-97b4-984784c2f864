package com.ly.titc.pms.member.mediator.rpc.dubbo.cashier;

import com.ly.titc.cashier.dubbo.entity.request.trade.*;
import com.ly.titc.cashier.dubbo.entity.response.trade.CashierAccountSettleDeductResp;
import com.ly.titc.cashier.dubbo.entity.response.trade.CashierAccountSettlePaymentResp;
import com.ly.titc.cashier.dubbo.interfaces.settleTrade.CashierSettlePaymentTradeDubboService;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/25
 */
@Slf4j
@Service
public class CashierSettlePaymentTradeDecorator {
    @DubboReference(group = "${cashier-dsf-dubbo-group}",timeout = 30000)

    @Resource
    private CashierSettlePaymentTradeDubboService cashierTradeDubboService;

    /**
     * 创建支付结算交易单
     * @param req
     * @return
     */
    public CashierAccountSettlePaymentResp createSettleTrade(CashierAccountSettlePaymentTradeReq req){
        Response<CashierAccountSettlePaymentResp> response = cashierTradeDubboService.createSettleTrade(req);
        return Response.getValidateData(response);
    }


    /**
     * 创建冲减交易单
     */
    public CashierAccountSettleDeductResp createDeductTrade(CashierAccountSettleDeductTradeReq req){
        Response<CashierAccountSettleDeductResp> response = cashierTradeDubboService.createDeductTrade(req);
        log.info("创建冲减交易单返回结果：{}",response);
        return Response.getValidateData(response);
    }

    /**
     * 创建预授权交易单
     */
    public CashierAccountSettlePaymentResp createPrePayTrade(CashierAccountSettlePaymentTradeReq req){
        Response<CashierAccountSettlePaymentResp> response = cashierTradeDubboService.createPrePayTrade(req);
        return Response.getValidateData(response);
    }


    /**
     * 取消预授权交易
     */
    public CashierAccountSettlePaymentResp cancelPrePayTrade(CashierAccountPrePayOpReq req){
        Response<CashierAccountSettlePaymentResp> response = cashierTradeDubboService.cancelPrePayTrade(req);
        return Response.getValidateData(response);
    }

    /**
     * 完成预授权交易
     */
    public CashierAccountSettlePaymentResp finishPrePayTrade(CashierAccountPrePayFinishReq req){
        Response<CashierAccountSettlePaymentResp> response = cashierTradeDubboService.finishPrePayTrade(req);
        return Response.getValidateData(response);
    }

    /**
     * 获取支付/预授权交易状态
     */
    public CashierAccountSettlePaymentResp getTradeState(CashierAccountSettlePaymentGetStateReq req){
        Response<CashierAccountSettlePaymentResp> response = cashierTradeDubboService.getTradeState(req);
        return Response.getValidateData(response);
    }


    /**
     * 获取冲减交易状态
     */
    public CashierAccountSettleDeductResp getDeductTradeState(CashierAccountSettleRefundGetStateReq req){
        Response<CashierAccountSettleDeductResp> response = cashierTradeDubboService.getDeductTradeState(req);
        return Response.getValidateData(response);
    }

}
