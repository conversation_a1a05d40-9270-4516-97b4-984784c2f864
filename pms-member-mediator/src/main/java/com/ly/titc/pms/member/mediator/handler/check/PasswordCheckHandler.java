package com.ly.titc.pms.member.mediator.handler.check;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author：rui
 * @name：VerifyCodeCheckHandler
 * @Date：2024-11-25 16:33
 * @Filename：VerifyCodeCheckHandler
 */
@Component
@Slf4j
public class PasswordCheckHandler extends AbstractRegisterCheckHandler {

    @Override
    public Integer getAction() {
        return MemberRegisterCheckEnum.PASSWORD.getAction();
    }

    @Override
    public void check(RegisterMemberDto dto) {
        if (StringUtils.isNotEmpty(dto.getPassword())) {
            if (!isValidPassword(dto.getPassword())) {
                throw new ServiceException(RespCodeEnum.MEMBER_10043);
            }
        }
    }


    public static boolean isValidPassword(String password) {
        if (password == null || password.length() < 6 || password.length() > 12) {
            return false;
        }
        boolean hasLetter = false;
        boolean hasDigit = false;
        boolean hasSpecialChar = false;

        String specialChars = "!#$%&*()-_=+/<>:;?@[]\\\\{}_~";

        for (char ch : password.toCharArray()) {
            if (Character.isLetter(ch)) {
                hasLetter = true;
            } else if (Character.isDigit(ch)) {
                hasDigit = true;
            } else if (specialChars.indexOf(ch) >= 0) {
                hasSpecialChar = true;
            } else {
                // 如果包含非字母、数字、特殊字符，则密码无效
                return false;
            }
        }
        return hasLetter && (hasDigit || hasSpecialChar);
    }


}
